import React from 'react';
import classNames from 'clsx';
import { getChildByType } from 'react-nanny';
import { twMerge } from 'tailwind-merge';
import type { CardSize } from './Card.types';
import { CardBody } from './components/CardBody';
import { CardFooter } from './components/CardFooter';
import { CardHeader } from './components/CardHeader';

type Components = {
  Header: typeof CardHeader;
  Body: typeof CardBody;
  Footer: typeof CardFooter;
};

export type CardProps = {
  children: React.ReactNode;
  size: CardSize;
  className?: string;
};

export const Card: React.FC<CardProps> & Components = ({ className, children, size, ...props }) => {
  const cardHeader = getChildByType(children, [CardHeader]) as React.ReactElement<any> | null;
  const cardBody = getChildByType(children, [CardBody]) as React.ReactElement<any> | null;
  const cardFooter = getChildByType(children, [CardFooter]) as React.ReactElement<any> | null;

  const sizedHeader = cardHeader ? React.cloneElement(cardHeader, { ...cardHeader.props, size }) : null;
  const sizedFooter = cardFooter ? React.cloneElement(cardFooter, { ...cardFooter.props, size }) : null;

  return (
    <div
      className={twMerge(classNames('bg-white shadow-sm flex flex-col border-gray-200 border-2 rounded-md', className))}
      {...props}
    >
      {sizedHeader}
      {cardBody}
      {sizedFooter}
    </div>
  );
};

Card.Header = CardHeader;
Card.Body = CardBody;
Card.Footer = CardFooter;
