import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Tabs } from './Tabs';

describe('Tabs', () => {
  it('renders the children', () => {
    render(<Tabs>content</Tabs>);

    expect(screen.getByText('content')).toBeInTheDocument();
  });

  describe('onChange', () => {
    it('trigger on change when clicked', async () => {
      const onChange = jest.fn();
      render(
        <Tabs onChange={onChange}>
          <Tabs.Tab>Tab1</Tabs.Tab>
          <Tabs.Tab>Tab2</Tabs.Tab>
        </Tabs>
      );

      await userEvent.click(screen.getByRole('tab', { name: 'Tab2' }));

      expect(onChange).toHaveBeenCalledTimes(1);
      expect(onChange).toHaveBeenCalledWith(expect.any(Object), 1);
    });
  });
});
