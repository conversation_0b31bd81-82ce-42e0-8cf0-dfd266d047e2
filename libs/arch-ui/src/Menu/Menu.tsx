import React, {
  type ComponentProps,
  type ComponentPropsWithoutRef,
  type ElementRef,
  forwardRef,
  type PropsWithChildren,
  type ReactNode,
  type Ref,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Menu, Menu as MenuPrimitive } from '@headlessui/react';
import { useComposedRefs } from '@radix-ui/react-compose-refs';
import { useMediaQuery } from '@shape-construction/hooks';
import { usePopper } from 'react-popper';
import { twMerge } from 'tailwind-merge';
import { breakpoints } from '../utils/breakpoints';
import { cn } from '../utils/classes';
import { renderChildren } from '../utils/render';

type MenuContextType = {
  triggerReference: React.RefObject<HTMLButtonElement | null>;
};

const MenuContext = React.createContext<MenuContextType>({
  triggerReference: { current: null },
});

/** @deprecated Use Dropdown */
export const MenuRoot: React.FC<ComponentProps<typeof MenuPrimitive>> = (props) => {
  const triggerReference = useRef<HTMLButtonElement | null>(null);

  return (
    <MenuContext.Provider value={{ triggerReference }}>
      <MenuPrimitive {...props} />
    </MenuContext.Provider>
  );
};
MenuRoot.displayName = 'Menu.Root';

export const MenuTrigger = forwardRef<
  ElementRef<typeof MenuPrimitive.Button>,
  ComponentProps<typeof MenuPrimitive.Button>
>((props, ref) => {
  const { triggerReference } = useContext(MenuContext);
  const composedRefs = useComposedRefs(ref, triggerReference);

  return <MenuPrimitive.Button ref={composedRefs} {...props} />;
});
MenuTrigger.displayName = 'Menu.Trigger';

export const MenuHeading = ({ children, className, ...props }: { children?: ReactNode; className?: string }) => {
  const headingClasses = twMerge('text-xs text-gray-400 leading-4 font-semibold tracking-wider uppercase', className);

  return (
    <div className="w-full h-8 px-4 pt-3 pb-1">
      <div className={headingClasses} {...props}>
        {children}
      </div>
    </div>
  );
};
MenuHeading.displayName = 'Menu.Heading';

export const MenuItems = forwardRef<ElementRef<typeof MenuPrimitive.Items>, ComponentProps<typeof MenuPrimitive.Items>>(
  ({ className, children, ...props }, ref) => {
    const { triggerReference } = useContext(MenuContext);
    const [popperReference, setPopperElement] = useState<HTMLDivElement | null>();
    const isLargeScreen = useMediaQuery(breakpoints.up('md'));
    const composedRefs = useComposedRefs(ref, setPopperElement) as React.Ref<HTMLDivElement>;
    const { styles, attributes, update } = usePopper(triggerReference.current, popperReference, {
      placement: 'bottom-end',
      strategy: 'fixed',
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, 8],
          },
        },
      ],
    });

    const popperProperties = isLargeScreen
      ? {
          style: styles.popper,
          ...attributes.popper,
        }
      : {};

    useEffect(() => {
      const element = triggerReference?.current;

      if (!element) return;

      const observer = new IntersectionObserver(() => {
        if (update) update();
      });
      observer.observe(element);

      return () => {
        observer.disconnect();
      };
    }, [triggerReference, update]);

    return (
      <MenuPrimitive.Items {...props}>
        {(state) => {
          if (state.open) document.body.style.overflow = 'hidden';
          else document.body.style.overflow = 'unset';

          return (
            <>
              {/* Menu does not support backdrop natively so, to accomplish that behaviour we are placing a
                  hidden button to work as a backdrop for small layout */}
              <MenuPrimitive.Button
                className={cn(
                  'z-popover fixed inset-0 bg-gray-800 opacity-75 backdrop-filter overflow-none',
                  'md:hidden'
                )}
              />
              <div
                ref={composedRefs}
                {...popperProperties}
                className={cn(
                  'z-popover flex flex-col items-start py-1 focus:outline-hidden bg-white shadow-lg ring-1 ring-black/5',
                  'fixed inset-x-0 bottom-0 rounded-t-md',
                  'md:relative md:w-56 md:rounded-md',
                  className
                )}
              >
                {renderChildren(children, state)}
              </div>
            </>
          );
        }}
      </MenuPrimitive.Items>
    );
  }
);
MenuItems.displayName = 'Menu.Items';

type OwnProps = PropsWithChildren<{
  className?: string;
  color?: 'default' | 'danger';
  icon?: React.FC<ComponentProps<'svg'>>;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
}>;
type ExcludedMenuItemProps = 'className' | 'color';

export type MenuItemProps = Omit<ComponentPropsWithoutRef<typeof Menu.Item>, ExcludedMenuItemProps> & OwnProps;

export const MenuItem = forwardRef<React.ElementRef<typeof Menu.Item>, MenuItemProps>(
  (
    {
      as = 'button',
      color = 'default',
      children,
      className,
      icon: Icon,
      startAdornment,
      endAdornment,
      ...props
    }: MenuItemProps,
    ref: Ref<HTMLElement>
  ) => {
    return (
      <Menu.Item
        as={as}
        ref={ref}
        className={cn(
          'w-full flex items-center gap-3 py-3.5 md:py-2 px-4 hover:bg-neutral-alpha-subltest-hovered aria-disabled:cursor-default aria-disabled:opacity-50',
          className
        )}
        {...props}
      >
        {startAdornment}
        {Icon && (
          <Icon
            aria-label="dropdown-item-icon"
            className={cn('h-4 w-4', {
              'text-neutral-subtle': color === 'default',
              'text-danger-subtle': color === 'danger',
            })}
          />
        )}
        <span
          className={cn('flex-1 text-left text-sm font-normal leading-5', {
            'text-neutral': color === 'default',
            'text-danger': color === 'danger',
          })}
        >
          {children}
        </span>
        {endAdornment}
      </Menu.Item>
    );
  }
);

MenuItem.displayName = 'Menu.Item';
