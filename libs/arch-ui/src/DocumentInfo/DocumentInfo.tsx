import React from 'react';
import { formatTimeAgo } from '@shape-construction/utils/DateTime';
import FileExtensionIcon from '../FileExtensionIcon';
import type { File } from '../types/File';
import { getByteSizeInMb } from '../utils/documents';

export interface DocumentInfoProps {
  document: File;
  userName?: string;
}

export const DocumentInfo: React.FC<DocumentInfoProps> = ({ document, userName }) => {
  const { byteSize, extension, filename, createdAt } = document;

  return (
    <div className="flex w-full items-center overflow-hidden">
      <FileExtensionIcon extension={extension} width="18" height="24" className="mr-3 shrink-0" />
      <div className="flex-1 overflow-hidden text-xs font-medium leading-4">
        <span className=" block w-full overflow-hidden text-ellipsis whitespace-nowrap text-gray-800">{filename}</span>
        <p className="text-gray-500">
          {byteSize && `${getByteSizeInMb(byteSize)} MB - ${userName || ''}`}
          {createdAt && ` ${formatTimeAgo(createdAt)}`}
        </p>
      </div>
    </div>
  );
};
