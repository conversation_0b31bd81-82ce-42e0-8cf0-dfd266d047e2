import React from 'react';
import type { Meta, StoryFn } from '@storybook/react-vite';
import { AvatarGroupStacked, type AvatarGroupStackedProps } from './AvatarGroupStacked';

export default {
  title: 'Foundation/Avatar/AvatarGroupStacked',
  component: AvatarGroupStacked,
} as Meta;

const sizes: AvatarGroupStackedProps['size'][] = ['sm', 'md', 'lg'];
const orders: AvatarGroupStackedProps['order'][] = ['bottom-to-top', 'top-to-bottom'];
const users: AvatarGroupStackedProps['users'] = [
  {
    id: 'user_1',
    name: 'User 1',
    avatarUrl: 'https://picsum.photos/id/1027/300',
  },
  {
    id: 'user_2',
    name: 'User 2',
    avatarUrl: 'https://picsum.photos/id/1027/300',
  },
  {
    id: 'user_3',
    name: 'User 3',
    avatarUrl: 'https://picsum.photos/id/1027/300',
  },
  {
    id: 'user_4',
    name: 'User 4',
    avatarUrl: 'https://picsum.photos/id/1027/300',
  },
];

const Template: React.FC<AvatarGroupStackedProps> = (props) => <AvatarGroupStacked {...props} />;

export const Sizes: StoryFn = () => (
  <div className="flex flex-col gap-y-10">
    {sizes.map((size) => (
      <div key={size}>
        <p>Size: {size}</p>
        <Template users={users} key={size} size={size} />
      </div>
    ))}
  </div>
);

export const Order: StoryFn = () => (
  <div className="flex flex-col gap-y-10">
    {orders.map((order) => (
      <div key={order}>
        <p>Order: {order}</p>
        <Template users={users} size="md" order={order} />
      </div>
    ))}
  </div>
);

export const Length: StoryFn = () => (
  <div className="flex flex-col gap-y-10">
    {[2, 3, 4].map((length) => (
      <div key={length}>
        <p>Length: {length}</p>
        <Template users={users} size="md" length={length} />
      </div>
    ))}
  </div>
);

export const ShowMore: StoryFn = () => (
  <div className="flex flex-col gap-y-10">
    {[true, false].map((showMore) => (
      <div key={String(showMore)}>
        <p>Show more: {String(showMore)}</p>
        <Template users={users} size="md" length={3} showMore={showMore} />
      </div>
    ))}
  </div>
);

export const HighlightedColor: StoryFn = () => (
  <div className="flex flex-col gap-y-10">
    <div>
      <p>Highlighted Color: Default</p>
      <Template users={users} size="md" length={3} showMore />
    </div>
    <div>
      <p>Highlighted Color: Custom - Indigo</p>
      <Template users={users} size="md" length={3} showMore getHighlightColor={() => 'primary'} />
    </div>
    <div>
      <p>Highlighted Color: Custom - Only Specific User</p>
      <Template
        users={users}
        size="md"
        length={3}
        showMore
        getHighlightColor={(user) => (user?.id === users[1]?.id ? 'primary' : undefined)}
      />
    </div>
  </div>
);

export const NoUsers: StoryFn = () => (
  <div className="flex flex-col gap-y-10">
    <div>
      <p>With Users</p>
      <Template users={users} size="md" length={3} />
    </div>
    <div>
      <p>Without Users</p>
      <Template users={[undefined, undefined, undefined]} size="md" length={3} />
    </div>
  </div>
);
