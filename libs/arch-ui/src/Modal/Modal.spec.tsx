import { render } from '@testing-library/react';
import ModalBaseModule from '../ModalBase';
import createMatchMedia from '../tests/create-match-media';
import { mediaQueryOptions } from '../utils/breakpoints';
import Modal from '.';

const defaultProps = {
  open: true,
  onClose: jest.fn(),
};

describe('<Modal.Root />', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  describe('when on small screens', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.md - 1);
    });

    it('renders Modal component in fullscreen', () => {
      const spyModal = jest.spyOn(ModalBaseModule, 'Root');

      render(
        <Modal.Root {...defaultProps}>
          <input />
        </Modal.Root>
      );

      const [props] = spyModal.mock.calls[0];
      expect(props.fullScreen).toBe(true);
      expect(props.outsidePad).toBe(false);
      expect(props.roundBorders).toBe(false);
    });
  });

  describe('when on large screens', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.md + 1);
    });

    it('renders Modal component', () => {
      const spyModal = jest.spyOn(ModalBaseModule, 'Root');

      render(
        <Modal.Root {...defaultProps}>
          <input />
        </Modal.Root>
      );

      const [props] = spyModal.mock.calls[0];
      expect(props.outsidePad).toBe(true);
      expect(props.roundBorders).toBe(true);
    });
  });
});

describe('<Modal.Footer />', () => {
  it('renders ModalFooter component with topBorder ', () => {
    const spyModalFooter = jest.spyOn(ModalBaseModule, 'Footer');

    render(<Modal.Footer />);

    const [props] = spyModalFooter.mock.calls[0];
    expect(props.topBorder).toBe(true);
  });
});

describe('<Modal.Header />', () => {
  it('renders ModalHeader component with bottomBorder ', () => {
    const spyModalHeader = jest.spyOn(ModalBaseModule, 'Header');

    render(<Modal.Header />);

    const [props] = spyModalHeader.mock.calls[0];
    expect(props.bottomBorder).toBe(true);
  });
});
