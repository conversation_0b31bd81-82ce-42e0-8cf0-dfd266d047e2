import React, { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import Badge from '../Badge';
import { SHAPE, SIZE } from '../Badge/Badge.types';
import FileExtensionIcon from '../FileExtensionIcon';
import RadioGroup from './';

type TemplateArgs = React.ComponentProps<typeof RadioGroup.Root> & { withIcon?: boolean };

type Story = StoryObj<TemplateArgs>;
const meta: Meta<typeof RadioGroup.Root> = {
  title: 'Input/RadioGroup',
  component: RadioGroup.Root,
  subcomponents: {
    Root: RadioGroup.Root,
    Items: RadioGroup.Items,
    Item: RadioGroup.Item,
    Label: RadioGroup.Label,
  },
};
export default meta;

const items = [
  {
    value: 'item-default',
    label: 'Default value',
    description: 'I am the default value',
    icon: <FileExtensionIcon extension="csv" className="self-stretch" />,
  },
  {
    value: 'item-badge',
    label: 'Badge item',
    description: 'I am an item with badge',
    badge: <Badge label="PRO" shape={SHAPE.ROUNDED} size={SIZE.EXTRA_SMALL} />,
    icon: <FileExtensionIcon extension="xlsx" className="self-stretch" />,
  },
  {
    value: 'item-error',
    label: 'Error item',
    description: 'I am an item with error',
    icon: <FileExtensionIcon extension="svg" className="self-stretch" />,
    error: true,
  },
  {
    value: 'disabled',
    label: 'Disabled item',
    description: 'I am a disabled item',
    icon: <FileExtensionIcon extension="pdf" className="self-stretch" />,
    disabled: true,
  },
];

const DefaultTemplate = (props: TemplateArgs) => (
  <RadioGroup.Root {...props}>
    <RadioGroup.Label description="Supporting text">Group Label</RadioGroup.Label>
    <RadioGroup.Items>
      {items.map(({ icon, ...item }) => (
        <RadioGroup.Item key={item.value} {...item}>
          <RadioGroup.Label description={item.description}>{item.label}</RadioGroup.Label>
        </RadioGroup.Item>
      ))}
    </RadioGroup.Items>
  </RadioGroup.Root>
);

const Template = ({ withIcon, ...props }: TemplateArgs) => (
  <>
    {['Vertical', 'Horizontal'].map((orientation) => (
      <RadioGroup.Root
        key={orientation}
        {...props}
        orientation={orientation.toLowerCase() as React.ComponentProps<typeof RadioGroup.Root>['orientation']}
        className="mb-8"
      >
        <RadioGroup.Label description="Supporting text">{orientation} orientation</RadioGroup.Label>
        <RadioGroup.Items>
          {items.map(({ icon, ...item }) => (
            <RadioGroup.Item key={item.value} {...item} icon={withIcon ? icon : null}>
              <RadioGroup.Label description={item.description}>{item.label}</RadioGroup.Label>
            </RadioGroup.Item>
          ))}
        </RadioGroup.Items>
      </RadioGroup.Root>
    ))}
  </>
);

const LabelPositionTemplate = ({ withIcon, ...props }: TemplateArgs) => (
  <div className="flex flex-col gap-8">
    <RadioGroup.Root {...props} className="flex-row">
      <RadioGroup.Label description="Supporting text">Inline Group Label</RadioGroup.Label>
      <RadioGroup.Items>
        {items.map(({ icon, ...item }) => (
          <RadioGroup.Item key={item.value} {...item}>
            <RadioGroup.Label>{item.label}</RadioGroup.Label>
          </RadioGroup.Item>
        ))}
      </RadioGroup.Items>
    </RadioGroup.Root>
    <RadioGroup.Root {...props} variant="card" orientation="horizontal">
      <RadioGroup.Label description="Supporting text">Bottom Item Label</RadioGroup.Label>
      <RadioGroup.Items>
        {items.map((item) => (
          <RadioGroup.Item key={item.value} {...item} className="flex-col">
            <RadioGroup.Label description={item.description} className="text-center">
              {item.label}
            </RadioGroup.Label>
          </RadioGroup.Item>
        ))}
      </RadioGroup.Items>
    </RadioGroup.Root>
  </div>
);

const ControlledTemplate = (props: React.ComponentProps<typeof RadioGroup.Root>) => {
  const [value, setValue] = useState('item-default');
  return (
    <RadioGroup.Root {...props} value={value}>
      <RadioGroup.Label description="Supporting text">
        Current value: <span className="font-bold text-brand-bold">{value}</span>
      </RadioGroup.Label>
      {items.map(({ icon, ...item }) => (
        <RadioGroup.Item
          key={item.value}
          {...item}
          onClick={() => {
            setValue(item.value);
          }}
        >
          <RadioGroup.Label description={item.description}>{item.label}</RadioGroup.Label>
        </RadioGroup.Item>
      ))}
    </RadioGroup.Root>
  );
};

export const Default: Story = {
  render: DefaultTemplate,
  args: { defaultValue: items[0].value },
};

export const SimpleVariant: Story = {
  render: Template,
  args: { defaultValue: items[0].value },
};

export const SimpleVariantWithIcon: Story = {
  render: Template,
  args: { defaultValue: items[0].value, withIcon: true },
};

export const CardVariant: Story = {
  render: Template,
  args: { defaultValue: items[0].value, variant: 'card' },
};

export const CardVariantWithIcon: Story = {
  render: Template,
  args: { defaultValue: items[0].value, variant: 'card', withIcon: true },
};

export const ListVariant: Story = {
  render: Template,
  args: { defaultValue: items[0].value, variant: 'list' },
};

export const ListVariantWithIcon: Story = {
  render: Template,
  args: { defaultValue: items[0].value, variant: 'list', withIcon: true },
};

export const WithGroupError: Story = {
  render: Template,
  args: { defaultValue: items[0].value, touched: true, error: 'There is an error' },
};

export const LabelPositions: Story = {
  render: LabelPositionTemplate,
  args: { defaultValue: items[0].value },
};

export const Controlled: Story = {
  render: ControlledTemplate,
};
