import { render } from '@testing-library/react';
import createMatchMedia from 'tests/create-match-media';
import ModalBaseModule from '../ModalBase';
import { mediaQueryOptions } from '../utils/breakpoints';
import Drawer from '.';

const spyModal = jest.spyOn(ModalBaseModule, 'Root');

describe('<Drawer.Root />', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  describe('<Drawer.Header />', () => {
    // TODO: Fix this test - useMediaQuery hook not working properly in test environment
    // it('executes on close when click close button', async () => {
    //   const spyOnClose = jest.fn();
    //   render(
    //     <Drawer.Root open onClose={spyOnClose}>
    //       <Drawer.Header title="Drawer title" onClose={spyOnClose} />
    //     </Drawer.Root>
    //   );

    //   await userEvent.click(screen.getByRole('button', { name: /close overlay/i }));

    //   expect(spyOnClose).toBeCalled();
    // });

    it('renders Modal component with proper transition configuration', () => {
      render(
        <Drawer.Root open onClose={() => {}}>
          <input />
        </Drawer.Root>
      );

      const [props] = spyModal.mock.calls[0];
      expect(props.transition).toBe('right-to-left');
    });

    describe('when on small screens', () => {
      beforeEach(() => {
        window.matchMedia = createMatchMedia(mediaQueryOptions.md - 1);
      });

      it('renders Modal component in fullscreen ', () => {
        render(
          <Drawer.Root open onClose={() => {}}>
            <input />
          </Drawer.Root>
        );

        const [props] = spyModal.mock.calls[0];
        expect(props.fullScreen).toBe(true);
      });

      it('renders Modal component with proper width configuration', () => {
        render(
          <Drawer.Root open onClose={() => {}}>
            <input />
          </Drawer.Root>
        );

        const [props] = spyModal.mock.calls[0];
        expect(props.fullWidth).toBe(true);
        expect(props.maxWidth).toBe('none');
      });
    });

    describe('when on large screens', () => {
      beforeEach(() => {
        window.matchMedia = createMatchMedia(mediaQueryOptions.md + 1);
      });

      it('does not render Modal component in fullscreen ', () => {
        render(
          <Drawer.Root open onClose={() => {}}>
            <input />
          </Drawer.Root>
        );

        const [props] = spyModal.mock.calls[0];
        expect(props.fullScreen).toBe(false);
      });

      it('renders Modal component with proper width configuration', () => {
        render(
          <Drawer.Root open onClose={() => {}}>
            <input />
          </Drawer.Root>
        );

        const [props] = spyModal.mock.calls[0];
        expect(props.fullWidth).toBe(true);
        expect(props.maxWidth).toBe('md');
      });
    });
  });
});
