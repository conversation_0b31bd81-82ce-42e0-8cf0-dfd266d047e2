import React, { type ComponentProps } from 'react';

export const ErrorIcon: React.FC<ComponentProps<'svg'>> = (props) => (
  <svg width="240" height="160" viewBox="0 0 240 160" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <title>Error icon</title>
    <rect width="240" height="160" fill="white" />
    <g opacity="0.9">
      <path opacity="0.9" d="M218.037 123.941H177.043V137.919H218.037V123.941Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M173.366 123.941H132.372V137.919H173.366V123.941Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M128.695 123.941H87.7002V137.919H128.695V123.941Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M84.0344 123.941H43.04V137.919H84.0344V123.941Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M218.037 90.5801H177.043V104.558H218.037V90.5801Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M173.366 90.5801H132.372V104.558H173.366V90.5801Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M128.695 90.5801H87.7002V104.558H128.695V90.5801Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M84.0344 90.5801H43.04V104.558H84.0344V90.5801Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M173.366 57.2304H132.372V71.2085H173.366V57.2304Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M128.694 57.2304H87.7V71.2085H128.694V57.2304Z" fill="#F3F4F6" />
      <path
        opacity="0.9"
        d="M133.995 20.2461L131.438 33.9883L171.741 41.4871L174.298 27.7449L133.995 20.2461Z"
        fill="#F3F4F6"
      />
      <path opacity="0.9" d="M128.695 23.8691H87.7002V37.8471H128.695V23.8691Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M195.696 107.262H154.702V121.24H195.696V107.262Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M151.036 107.262H110.042V121.24H151.036V107.262Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M106.365 107.262H65.3701V121.24H106.365V107.262Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M61.9944 107.262H21V121.24H61.9944V107.262Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M195.696 73.9004H154.702V87.8784H195.696V73.9004Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M151.036 73.9004H110.042V87.8784H151.036V73.9004Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M106.365 73.9004H65.3701V87.8784H106.365V73.9004Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M151.036 40.5488H110.042V54.5269H151.036V40.5488Z" fill="#F3F4F6" />
      <path opacity="0.9" d="M106.365 40.5488H65.3701V54.5269H106.365V40.5488Z" fill="#F3F4F6" />
      <path
        opacity="0.9"
        d="M150.814 6.99996L109.819 10.6895L111.077 24.6674L152.072 20.9779L150.814 6.99996Z"
        fill="#F3F4F6"
      />
    </g>
    <path
      d="M148.288 146.156H90.9097L112.252 58.3323C112.799 56.5471 113.799 55.0048 115.113 53.9149C116.428 52.8249 117.994 52.2402 119.599 52.2402C121.204 52.2402 122.771 52.8249 124.085 53.9149C125.4 55.0048 126.399 56.5471 126.946 58.3323L148.288 146.156Z"
      fill="#6366F1"
    />
    <path d="M133.917 87.0053H105.281L109.774 68.4902H129.424L133.917 87.0053Z" fill="#E5E7EB" />
    <path d="M141.379 117.736H97.8184L102.311 99.2031H136.871L141.379 117.736Z" fill="#E5E7EB" />
    <g style={{ mixBlendMode: 'multiply' }} opacity="0.08">
      <path
        d="M148.273 146.155H90.9097L111.221 62.5371C109.482 116.644 138.01 133.63 146.134 137.362L148.273 146.155Z"
        fill="black"
      />
    </g>
    <path d="M159.182 146.156H80V152H159.182V146.156Z" fill="#6366F1" />
  </svg>
);
