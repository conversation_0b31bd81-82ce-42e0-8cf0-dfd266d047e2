import React, { type ComponentPropsWithoutRef, createContext, forwardRef, useContext, useId, useRef } from 'react';
import { useComposedRefs } from '@radix-ui/react-compose-refs';
import { Slot } from 'radix-ui';
import { twMerge } from 'tailwind-merge';

type FileUploadContextType = {
  id?: string;
  inputRef: React.RefObject<HTMLInputElement | null>;
};

const FileUploadContext = createContext<FileUploadContextType>({
  id: undefined,
  inputRef: { current: null },
});

type FileUploadRootProps = Omit<ComponentPropsWithoutRef<'input'>, 'onChange'> & {
  onChange?: (files: File[]) => void;
};
export const FileUploadRoot = forwardRef<React.ElementRef<'input'>, FileUploadRootProps>(
  ({ children, className, onChange, id, ...inputProps }, ref) => {
    const randomId = useId();
    const elementId = id || randomId;
    const inputRef = useRef<HTMLInputElement | null>(null);
    const composedRef = useComposedRefs(ref, inputRef);

    const handleChange: ComponentPropsWithoutRef<'input'>['onChange'] = (event) => {
      const eventFiles = event.target.files;
      if (!eventFiles) return;

      onChange?.(Array.from(eventFiles));
      if (inputRef.current) inputRef.current.value = '';
    };

    return (
      <FileUploadContext.Provider value={{ inputRef, id: elementId }}>
        <div className={className}>
          <input ref={composedRef} type="file" hidden id={elementId} onChange={handleChange} {...inputProps} />
          {children}
        </div>
      </FileUploadContext.Provider>
    );
  }
);
FileUploadRoot.displayName = 'FileUpload.Root';

export const FileUploadTrigger = ({
  asChild,
  onClick,
  ...props
}: ComponentPropsWithoutRef<'button'> & { asChild?: boolean }) => {
  const { inputRef } = useContext(FileUploadContext);

  const handleClick: ComponentPropsWithoutRef<'button'>['onClick'] = (event) => {
    onClick?.(event);
    inputRef.current?.click();
  };

  return asChild ? (
    <Slot.Root onClick={handleClick} {...props} />
  ) : (
    <button onClick={handleClick} type="button" {...props} />
  );
};
FileUploadTrigger.displayName = 'FileUpload.Trigger';

export const FileUploadLabel = ({ className, ...props }: ComponentPropsWithoutRef<'label'>) => {
  const { id } = useContext(FileUploadContext);
  const callbackProps: Pick<ComponentPropsWithoutRef<'label'>, 'onClick'> = {
    onClick: (e) => e.stopPropagation(),
  };

  return (
    <label
      htmlFor={id}
      className={twMerge('cursor-pointer flex flex-col items-center justify-center', className)}
      {...callbackProps}
      {...props}
    />
  );
};
FileUploadLabel.displayName = 'FileUpload.Label';
