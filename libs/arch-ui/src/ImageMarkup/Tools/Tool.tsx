import React, { type FC, type ReactElement } from 'react';
import classNames from 'clsx';
import { twMerge } from 'tailwind-merge';
import type { ButtonBaseProps } from '../../Button/ButtonBase';
import { getButtonClasses } from '../../Button/ButtonClasses';
import { type Color, configuration } from '../colors';

export type ToolProps = Omit<ButtonBaseProps, 'color' | 'fullWidth' | 'rounded' | 'size' | 'variant'> & {
  /**
   * When a tool is active it shows an outline around the button
   */
  active?: boolean;
  /**
   * Color used for the active property
   */
  color?: Color;
  /**
   * Icon component to be rendered inside button component
   */
  icon: ReactElement;
  variant?: ButtonBaseProps['variant'];
};

// FIXME: This component needs to be refactored and analised.
// Using it as an extension of the BaseButton is just a quick fix.
export const Tool: FC<ToolProps> = ({
  active = false,
  color = 'gray',
  variant = 'outlined',
  icon,
  disabled = false,
  ...props
}) => {
  const { active: activeColor } = configuration[color];

  const { buttonClasses } = getButtonClasses({
    color: 'secondary',
    variant,
    disabled,
    fullWidth: false,
    shape: 'standard',
    size: 'md',
  });

  const toolClassNames = twMerge(
    classNames(
      buttonClasses,
      `text-gray-500 bg-white border-gray-300 hover:bg-gray-50 focus:${activeColor}`,
      { [activeColor]: active },
      { 'ring-2 ring-offset-2': active },
      props.className
    )
  );

  const iconElement = icon as React.ReactElement<any>;
  const Icon = React.cloneElement(iconElement, {
    className: classNames('h-5 w-5', iconElement.props.className),
  });

  return (
    <button {...props} type="button" className={toolClassNames} disabled={disabled}>
      {Icon}
    </button>
  );
};
