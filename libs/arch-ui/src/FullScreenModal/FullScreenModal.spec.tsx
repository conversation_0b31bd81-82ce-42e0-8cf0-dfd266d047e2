import { render } from '@testing-library/react';
import ModalBase from '../ModalBase';
import createMatchMedia from '../tests/create-match-media';
import { mediaQueryOptions } from '../utils/breakpoints';
import FullScreenModal from '.';

const defaultProps = {
  open: true,
  onClose: jest.fn(),
};

describe('<FullScreenModal.Root />', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  describe('when on small screens', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.md - 1);
    });

    it('renders Modal component in fullscreen ', () => {
      const spyModal = jest.spyOn(ModalBase, 'Root');

      render(
        <FullScreenModal.Root {...defaultProps}>
          <input />
        </FullScreenModal.Root>
      );

      const [props] = spyModal.mock.calls[0];
      expect(props.fullScreen).toBe(true);
      expect(props.outsidePad).toBe(false);
      expect(props.roundBorders).toBe(false);
    });
  });

  describe('when on large screens', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.md + 1);
    });

    it('renders Modal component in padded fullscreen ', () => {
      const spyModal = jest.spyOn(ModalBase, 'Root');

      render(
        <FullScreenModal.Root {...defaultProps}>
          <input />
        </FullScreenModal.Root>
      );

      const [props] = spyModal.mock.calls[0];
      expect(props.fullScreen).toBe(true);
      expect(props.outsidePad).toBe(true);
      expect(props.roundBorders).toBe(true);
    });
  });
});

describe('<FullScreenModal.Footer />', () => {
  it('renders ModalFooter component with topBorder ', () => {
    const spyModalFooter = jest.spyOn(ModalBase, 'Footer');

    render(<FullScreenModal.Footer />);

    const [props] = spyModalFooter.mock.calls[0];
    expect(props.topBorder).toBe(true);
  });
});

describe('<FullScreenModal.Header />', () => {
  it('renders ModalHeader component with bottomBorder ', () => {
    const spyModalHeader = jest.spyOn(ModalBase, 'Header');

    render(<FullScreenModal.Header />);

    const [props] = spyModalHeader.mock.calls[0];
    expect(props.bottomBorder).toBe(true);
  });
});
