{"name": "@shape-construction/arch-ui", "version": "0.2.455", "description": "Shared UI components for our web apps", "main": "./src/index.ts", "types": "./src/index.ts", "engines": {"node": "v22.18.0", "pnpm": "10.15.0"}, "scripts": {"build:storybook": "storybook build", "compile": "npx tsc", "dev": "npx tsc --watch", "generate:component": "plop", "generate:icons": "figma-export use-config && pnpm run format src/Icons", "generate:theme": "./scripts/theme/index.js && pnpm run format src/theme", "link": "./scripts/link.sh", "lint": "biome check .", "lint:autofix": "biome check --write .", "format": "biome format --write", "storybook": "storybook dev -p 6006", "test": "jest", "test:ci": "jest --ci --coverage"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "7.28.0", "@biomejs/biome": "2.0.6", "@figma-export/cli": "5.0.1", "@figma-export/output-components-as-svgr": "5.0.1", "@storybook/addon-docs": "9.1.3", "@storybook/react-vite": "9.1.3", "@svgr/plugin-jsx": "8.1.0", "@svgr/plugin-svgo": "8.1.0", "@testing-library/dom": "10.4.0 ", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@testing-library/user-event": "14.6.1", "@types/css-mediaquery": "^0.1.1", "@types/jest": "29.4.0", "@types/jest-axe": "^3.5.5", "@types/lodash.isequal": "4.5.8", "@types/react": "19.0.10", "@types/react-dom": "19.1.7", "@types/react-highlight-words": "^0.16.4", "babel-loader": "9.1.3", "jest": "29.4.3", "jest-axe": "^7.0.0", "npm": "8.15.0", "plop": "4.0.1", "postcss": "8.4.13", "react": "19.0.0", "react-dom": "19.0.0", "storybook": "9.1.3", "storybook-addon-pseudo-states": "9.1.3", "ts-jest": "^29.4.0", "typescript": "5.8.3", "vite": "7.1.3"}, "dependencies": {"@dnd-kit/core": "6.1.0", "@dnd-kit/sortable": "8.0.0", "@headlessui/react": "1.7.15", "@headlessui/tailwindcss": "^0.2.1", "@heroicons/react": "2.0.13", "@minoru/react-dnd-treeview": "3.4.1", "@popperjs/core": "2.11.6", "@radix-ui/react-compose-refs": "1.1.2", "@shape-construction/hooks": "workspace:*", "@shape-construction/utils": "workspace:*", "@storybook/addon-a11y": "9.1.3", "@storybook/addon-themes": "9.1.3", "@tailwindcss/forms": "0.5.10", "@tailwindcss/postcss": "4.1.10", "@tailwindcss/typography": "0.5.16", "@types/react-image-gallery": "1.2.4", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "css-mediaquery": "^0.1.2", "formik": "^2.2.9", "jest-environment-jsdom": "29.7.0", "lodash.isequal": "4.5.0", "radix-ui": "1.4.2", "react-day-picker": "8.8.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-highlight-words": "^0.17.0", "react-hot-toast": "2.6.0", "react-image-gallery": "1.4.0", "react-image-lightbox": "^5.1.4", "react-nanny": "^2.14.0", "react-popper": "^2.3.0", "react-spring-lightbox": "1.7.1", "resize-observer-polyfill": "1.5.1", "tailwind-merge": "2.5.4", "tailwindcss": "4.1.10", "tailwindcss-animate": "1.0.7"}}