import { faker } from '@faker-js/faker';
import { formatDateISO, formatISO } from '@shape-construction/utils/DateTime';
import type {
  ShiftReportActivitySchema,
  ShiftReportBasicDetailsSchema,
  ShiftReportCommentSchema,
  ShiftReportCompletionSchema,
  ShiftReportContractForceSchema,
  ShiftReportDayCompletionSchema,
  ShiftReportDownTimeSchema,
  ShiftReportEquipmentSchema,
  ShiftReportMaterialSchema,
  ShiftReportPublishSchema,
  ShiftReportQualityIndicatorsSchema,
  ShiftReportResourceAllocationSchema,
  ShiftReportSafetyHealthEnvironmentSchema,
  ShiftReportSchema,
} from '../src/types';
import { documentReferenceAndImageDocumentFactory } from './documents';
import type { Factory } from './utils';

export const shiftReportsAvailableActions: Factory<ShiftReportBasicDetailsSchema['availableActions']> = (
  availableActionsProperties
) => ({
  archive: true,
  edit: true,
  publish: true,
  export: false,
  restore: false,
  listCollaboratorsComments: false,
  listPublicComments: false,
  editRootFields: false,
  delete: true,
  ...availableActionsProperties,
});

export const shiftReportsActivityFactory: Factory<ShiftReportActivitySchema> = (props) => ({
  comment: 'comment-0',
  description: 'description-0',
  id: 'id-0',
  locationId: 'location-0',
  planned: 'planned',
  quantity: 1,
  shiftActivityId: 'shiftActivity-0',
  teamMemberId: 1,
  units: 'unit',
  documentCount: 0,
  ...props,
});

export const shiftReportsLinkFactory: Factory<ShiftReportResourceAllocationSchema> = (
  props
): ShiftReportResourceAllocationSchema => ({
  allocationId: 'id-0',
  id: 'id-0',
  quantity: 10,
  ...props,
});

export const shiftReportsContractForceFactory: Factory<ShiftReportContractForceSchema> = (props) => ({
  comment: 'comment-0',
  hours: 10,
  id: 'id-0',
  name: 'Zé Manel',
  organisation: 'Org-0',
  role: 'Supervisor',
  activities: [shiftReportsLinkFactory()],
  downTimes: [],
  teamMemberId: 1,
  documentCount: 0,
  organisationResourceId: null,
  personResourceId: null,
  roleResourceId: null,
  ...props,
});

export const shiftReportsDownTimeFactory: Factory<ShiftReportDownTimeSchema> = (props) => ({
  causalType: 'Causal Type 0',
  id: 'id-0',
  issueId: null,
  issueDescription: 'Electrician oozed the dragline',
  teamMemberId: 1,
  timeLost: 1,
  documentCount: 0,
  ...props,
});

export const shiftReportsEquipmentFactory: Factory<ShiftReportEquipmentSchema> = (props) => ({
  description: 'P1 Barge',
  equipmentId: 'equipment-0',
  hours: 10,
  id: 'id-0',
  quantity: 1,
  activities: [shiftReportsLinkFactory()],
  downTimes: [],
  teamMemberId: 1,
  documentCount: 0,
  equipmentResourceId: null,
  ...props,
});

export const shiftReportsSafetyHealthEnvironmentFactory: Factory<ShiftReportSafetyHealthEnvironmentSchema> = (
  props
) => ({
  id: 'id-0',
  safetyNote: 'safety-note-0',
  teamMemberId: 1,
  documentCount: 0,
  ...props,
});

export const shiftReportsMaterialFactory: Factory<ShiftReportMaterialSchema> = (props) => ({
  description: 'concrete mix',
  id: 'id-0',
  quantity: 1,
  teamMemberId: 1,
  units: '10',
  activities: [shiftReportsLinkFactory()],
  downTimes: [],
  documentCount: 0,
  materialResourceId: null,
  ...props,
});

export const shiftReportsListItemFactory: Factory<ShiftReportBasicDetailsSchema> = (props) => {
  const now = formatISO(new Date());
  return {
    approverId: null,
    archived: false,
    availableActions: shiftReportsAvailableActions(),
    collaboratorsTeamMemberIds: [],
    completionQualityScore: 30,
    contractorName: 'contractor',
    createdAt: now,
    documentCount: null,
    id: 'shift-report-0',
    projectId: 'project-0',
    publishedAt: now,
    reportDate: formatDateISO(new Date(), 'Europe/London'),
    reportTitle: 'title',
    shiftType: 'shiftType-0',
    state: 'draft',
    teamId: 'team-1',
    teamMemberId: 1,
    ...props,
  };
};

export const shiftReportsFactory: Factory<ShiftReportSchema> = (props) => {
  const basicDetails = shiftReportsListItemFactory();
  return {
    ...basicDetails,
    activities: [shiftReportsActivityFactory()],
    clientDocumentReferenceNumber: 'clientDocumentReferenceNumber-0',
    contractForces: [shiftReportsContractForceFactory()],
    downTimes: [shiftReportsDownTimeFactory()],
    equipments: [shiftReportsEquipmentFactory()],
    internalDocumentReferenceNumber: 'internalDocumentReferenceNumber-0',
    materials: [shiftReportsMaterialFactory()],
    notes: 'note-0',
    projectNumber: 'project-0',
    safetyHealthEnvironments: [shiftReportsSafetyHealthEnvironmentFactory()],
    shiftEnd: '05:00 pm',
    shiftStart: '08:00 am',
    visibility: 'private',
    visibilitySpecificTeamIds: ['team-1'],
    weatherDescription: 'Partly clouded',
    weatherTemperature: '14º - 20º',
    ...props,
  };
};

export const shiftReportsPublishFactory: Factory<ShiftReportPublishSchema> = (props) => ({
  exportQueuedTask: null,
  shiftReport: shiftReportsFactory(),
  ...props,
});

export const shiftReportCommentFactory: Factory<ShiftReportCommentSchema> = (props) => {
  const now = formatISO(new Date());

  return {
    createdAt: now,
    id: faker.string.uuid(),
    attachments: [],
    plainText: 'This is a comment',
    richText: {},
    teamMemberId: faker.number.int(),
    availableActions: shiftReportCommentAvailableActionsFactory(),
    ...props,
  };
};

export const shiftReportCommentAvailableActionsFactory: Factory<ShiftReportCommentSchema['availableActions']> = (
  props
) => {
  return {
    delete: false,
    gracePeriodUntil: null,
    ...props,
  };
};

export const shiftReportCommentAttachment = documentReferenceAndImageDocumentFactory;

export const shiftReportCompletionFactory: Factory<ShiftReportCompletionSchema> = (props) => ({
  id: faker.string.uuid(),
  authorId: 6,
  authorNewId: faker.string.uuid(),
  reportDate: formatDateISO(new Date(), 'Europe/London'),
  reportTitle: 'The Shift report',
  ...props,
});

export const shiftReportDayCompletionFactory: Factory<ShiftReportDayCompletionSchema> = (props) => {
  const reportDate = props?.reportDate ?? formatDateISO(new Date(), 'Europe/London');

  return {
    reportDate,
    shiftReports: [shiftReportCompletionFactory({ reportDate })],
    ...props,
  };
};

export const shiftReportQualityIndicatorsFactory: Factory<ShiftReportQualityIndicatorsSchema> = (props) => {
  return {
    currentScore: {
      percentage: {
        completed: 0,
        total: 100,
      },
    },
    basics: {
      items: {
        activityOrDowntime: false,
        reportDate: false,
        reportTitle: false,
        weatherDescription: false,
      },
      percentage: {
        completed: 0,
        total: 20,
      },
    },
    people: {
      percentage: {
        completed: 0,
        total: 20,
      },
    },
    equipment: {
      percentage: {
        completed: 0,
        total: 10,
      },
    },
    material: {
      percentage: {
        completed: 0,
        total: 10,
      },
    },
    evidence: {
      items: {
        completed: 0,
        total: 1,
      },
      percentage: {
        completed: 0,
        total: 20,
      },
    },
    allocations: {
      items: {
        completed: 0,
        total: 1,
      },
      percentage: {
        completed: 0,
        total: 20,
      },
    },
    ...props,
  };
};
