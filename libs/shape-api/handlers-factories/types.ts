import type { DefaultBodyType, PathParams, StrictRequest } from 'msw';

type HttpRequestResolverExtras<Params extends PathParams> = {
  params: Params;
  cookies: Record<string, string>;
};

type ResponseResolverInfo<
  ResolverExtraInfo extends Record<string, unknown>,
  RequestBodyType extends DefaultBodyType = DefaultBodyType,
> = {
  request: StrictRequest<RequestBodyType>;
  requestId: string;
} & ResolverExtraInfo;
type MaybePromise<T> = T | Promise<T>;

export type RequestHandlerOptions = { once: boolean };

export type Resolver<TParams extends PathParams, RequestBodyType extends DefaultBodyType, TResponseBodyType> = (
  info: ResponseResolverInfo<HttpRequestResolverExtras<TParams>, RequestBodyType>
) => MaybePromise<TResponseBodyType>;
