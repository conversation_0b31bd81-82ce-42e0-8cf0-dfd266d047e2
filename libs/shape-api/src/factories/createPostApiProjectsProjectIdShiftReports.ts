// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type {
  PostApiProjectsProjectIdShiftReportsPathParamsSchema,
  PostApiProjectsProjectIdShiftReportsMutationRequestSchema,
  PostApiProjectsProjectIdShiftReportsMutationResponseSchema,
} from '../types/postApiProjectsProjectIdShiftReportsSchema';
import { createAuthenticationError } from './createAuthenticationError';
import { createError } from './createError';
import { createShiftReport } from './createShiftReport';
import { createShiftReportVisibility } from './createShiftReportVisibility';
import { faker } from '@faker-js/faker';

export function createPostApiProjectsProjectIdShiftReportsPathParams(
  data?: Partial<PostApiProjectsProjectIdShiftReportsPathParamsSchema>
): PostApiProjectsProjectIdShiftReportsPathParamsSchema {
  faker.seed([100]);
  return {
    ...{ project_id: faker.string.uuid() },
    ...(data || {}),
  };
}

/**
 * @description Shift report created
 */
export function createPostApiProjectsProjectIdShiftReports201() {
  faker.seed([100]);
  return createShiftReport();
}

/**
 * @description Authentication required
 */
export function createPostApiProjectsProjectIdShiftReports401() {
  faker.seed([100]);
  return createAuthenticationError();
}

/**
 * @description Not authorised
 */
export function createPostApiProjectsProjectIdShiftReports403() {
  faker.seed([100]);
  return createError();
}

/**
 * @description Not found
 */
export function createPostApiProjectsProjectIdShiftReports404() {
  faker.seed([100]);
  return undefined;
}

/**
 * @description Create failed
 */
export function createPostApiProjectsProjectIdShiftReports422() {
  faker.seed([100]);
  return createError();
}

export function createPostApiProjectsProjectIdShiftReportsMutationRequest(
  data?: Partial<PostApiProjectsProjectIdShiftReportsMutationRequestSchema>
): PostApiProjectsProjectIdShiftReportsMutationRequestSchema {
  faker.seed([100]);
  return {
    ...{
      approver_id: faker.number.int(),
      client_document_reference_number: faker.string.alpha(),
      collaborators_team_member_ids: faker.helpers.multiple(() => faker.number.int()),
      contractor_logo_signed_id: faker.string.alpha(),
      contractor_name: faker.string.alpha(),
      internal_document_reference_number: faker.string.alpha(),
      notes: faker.string.alpha(),
      project_number: faker.string.alpha(),
      report_date: faker.date.anytime().toISOString().substring(0, 10),
      report_title: faker.string.alpha(),
      shift_end: faker.date.anytime().toISOString().substring(11, 19),
      shift_start: faker.date.anytime().toISOString().substring(11, 19),
      shift_type: faker.string.alpha(),
      visibility: createShiftReportVisibility(),
      visibility_specific_team_ids: faker.helpers.multiple(() => faker.string.uuid()),
      weather_description: faker.string.alpha(),
      weather_temperature: faker.string.alpha(),
      activities: faker.helpers.multiple(() => ({
        _id: faker.string.alpha(),
        comment: faker.string.alpha(),
        description: faker.string.alpha(),
        location_id: faker.string.uuid(),
        planned: faker.string.alpha(),
        quantity: faker.number.float(),
        shift_activity_id: faker.string.uuid(),
        units: faker.string.alpha(),
      })),
      contract_forces: faker.helpers.multiple(() => ({
        activities: faker.helpers.multiple(() => ({
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        comment: faker.string.alpha(),
        down_times: faker.helpers.multiple(() => ({
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        hours: faker.number.float(),
        organisation_resource_id: faker.string.uuid(),
        person_resource_id: faker.string.uuid(),
        role_resource_id: faker.string.uuid(),
      })),
      down_times: faker.helpers.multiple(() => ({
        _id: faker.string.alpha(),
        causal_type: faker.string.alpha(),
        issue_description: faker.string.alpha(),
        issue_id: faker.string.uuid(),
        time_lost: faker.number.float(),
      })),
      equipments: faker.helpers.multiple(() => ({
        activities: faker.helpers.multiple(() => ({
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        down_times: faker.helpers.multiple(() => ({
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        equipment_id: faker.string.alpha(),
        equipment_resource_id: faker.string.uuid(),
        hours: faker.number.float(),
        quantity: faker.number.float(),
      })),
      materials: faker.helpers.multiple(() => ({
        activities: faker.helpers.multiple(() => ({
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        down_times: faker.helpers.multiple(() => ({
          allocation_id: faker.string.uuid(),
          quantity: faker.number.float(),
        })),
        material_resource_id: faker.string.uuid(),
        quantity: faker.number.float(),
        units: faker.string.alpha(),
      })),
      safety_health_environments: faker.helpers.multiple(() => ({ safety_note: faker.string.alpha() })),
    },
    ...(data || {}),
  };
}

export function createPostApiProjectsProjectIdShiftReportsMutationResponse(
  data?: Partial<PostApiProjectsProjectIdShiftReportsMutationResponseSchema>
): PostApiProjectsProjectIdShiftReportsMutationResponseSchema {
  faker.seed([100]);
  return data || faker.helpers.arrayElement<any>([createPostApiProjectsProjectIdShiftReports201()]);
}
