// @ts-nocheck
/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type {
  PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreatePathParamsSchema,
  PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMutationResponseSchema,
} from '../types/postApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateSchema';
import { createAuthenticationError } from './createAuthenticationError';
import { createChangeSignalsBodyParameter } from './createChangeSignalsBodyParameter';
import { createError } from './createError';
import { createPotentialChange } from './createPotentialChange';
import { faker } from '@faker-js/faker';

export function createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreatePathParams(
  data?: Partial<PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreatePathParamsSchema>
): PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreatePathParamsSchema {
  faker.seed([100]);
  return {
    ...{ project_id: faker.string.uuid(), potential_change_id: faker.string.uuid() },
    ...(data || {}),
  };
}

/**
 * @description Creates potential change change signals links
 */
export function createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate200() {
  faker.seed([100]);
  return createPotentialChange();
}

/**
 * @description Batch create failed
 */
export function createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate400() {
  faker.seed([100]);
  return createError();
}

/**
 * @description Authentication required
 */
export function createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate401() {
  faker.seed([100]);
  return createAuthenticationError();
}

export function createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMutationRequest() {
  faker.seed([100]);
  return createChangeSignalsBodyParameter();
}

export function createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMutationResponse(
  data?: Partial<PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMutationResponseSchema>
): PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMutationResponseSchema {
  faker.seed([100]);
  return (
    data ||
    faker.helpers.arrayElement<any>([
      createPostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate200(),
    ])
  );
}
