/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportSchema } from './shiftReportSchema';
import type { ShiftReportVisibilitySchema } from './shiftReportVisibilitySchema';

export type PostApiProjectsProjectIdShiftReportsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Shift report created
 */
export type PostApiProjectsProjectIdShiftReports201Schema = ShiftReportSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReports401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftReports403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftReports404Schema = void;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdShiftReports422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftReportsMutationRequestSchema = {
  /**
   * @type integer
   */
  approver_id?: number | null;
  /**
   * @type string
   */
  client_document_reference_number?: string | null;
  /**
   * @type array | undefined
   */
  collaborators_team_member_ids?: number[];
  /**
   * @description The signed id given by the direct upload method
   * @type string
   */
  contractor_logo_signed_id?: string | null;
  /**
   * @type string
   */
  contractor_name?: string | null;
  /**
   * @type string
   */
  internal_document_reference_number?: string | null;
  /**
   * @type string
   */
  notes?: string | null;
  /**
   * @type string
   */
  project_number?: string | null;
  /**
   * @type string, date
   */
  report_date: string;
  /**
   * @type string
   */
  report_title?: string | null;
  /**
   * @type string, time
   */
  shift_end?: string | null;
  /**
   * @type string, time
   */
  shift_start?: string | null;
  /**
   * @type string
   */
  shift_type?: string | null;
  /**
   * @type string
   */
  visibility: ShiftReportVisibilitySchema;
  /**
   * @type array | undefined
   */
  visibility_specific_team_ids?: string[];
  /**
   * @type string
   */
  weather_description?: string | null;
  /**
   * @type string
   */
  weather_temperature?: string | null;
  /**
   * @type array | undefined
   */
  activities?: {
    /**
     * @description Temporary id, defined by the client, to allow resources to be linked to new activities.
     * @type string | undefined
     */
    _id?: string;
    /**
     * @type string
     */
    comment?: string | null;
    /**
     * @type string
     */
    description?: string | null;
    /**
     * @type string, uuid
     */
    location_id?: string | null;
    /**
     * @type string
     */
    planned?: string | null;
    /**
     * @type number, float
     */
    quantity?: number | null;
    /**
     * @type string, uuid
     */
    shift_activity_id?: string | null;
    /**
     * @type string
     */
    units?: string | null;
  }[];
  /**
   * @type array | undefined
   */
  contract_forces?: {
    /**
     * @type array | undefined
     */
    activities?: {
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type string
     */
    comment?: string | null;
    /**
     * @type array | undefined
     */
    down_times?: {
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type number, float
     */
    hours?: number | null;
    /**
     * @type string, uuid
     */
    organisation_resource_id?: string | null;
    /**
     * @type string, uuid
     */
    person_resource_id?: string | null;
    /**
     * @type string, uuid
     */
    role_resource_id?: string | null;
  }[];
  /**
   * @type array | undefined
   */
  down_times?: {
    /**
     * @description Temporary id, defined by the client, to allow resources to be linked to new down_times.
     * @type string | undefined
     */
    _id?: string;
    /**
     * @type string
     */
    causal_type?: string | null;
    /**
     * @type string
     */
    issue_description?: string | null;
    /**
     * @type string, uuid
     */
    issue_id?: string | null;
    /**
     * @type number, float
     */
    time_lost?: number | null;
  }[];
  /**
   * @type array | undefined
   */
  equipments?: {
    /**
     * @type array | undefined
     */
    activities?: {
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type array | undefined
     */
    down_times?: {
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type string
     */
    equipment_id?: string | null;
    /**
     * @type string, uuid
     */
    equipment_resource_id?: string | null;
    /**
     * @type number, float
     */
    hours?: number | null;
    /**
     * @type number, float
     */
    quantity?: number | null;
  }[];
  /**
   * @type array | undefined
   */
  materials?: {
    /**
     * @type array | undefined
     */
    activities?: {
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type array | undefined
     */
    down_times?: {
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type string, uuid
     */
    material_resource_id?: string | null;
    /**
     * @type number, float
     */
    quantity?: number | null;
    /**
     * @type string
     */
    units?: string | null;
  }[];
  /**
   * @type array | undefined
   */
  safety_health_environments?: {
    /**
     * @type string
     */
    safety_note?: string | null;
  }[];
};

export type PostApiProjectsProjectIdShiftReportsMutationResponseSchema = PostApiProjectsProjectIdShiftReports201Schema;

export type PostApiProjectsProjectIdShiftReportsSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReports201Schema;
  Request: PostApiProjectsProjectIdShiftReportsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftReportsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReports401Schema
    | PostApiProjectsProjectIdShiftReports403Schema
    | PostApiProjectsProjectIdShiftReports404Schema
    | PostApiProjectsProjectIdShiftReports422Schema;
};
