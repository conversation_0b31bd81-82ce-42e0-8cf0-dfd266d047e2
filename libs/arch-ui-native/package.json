{"name": "@shape-construction/arch-ui-native", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "engines": {"node": "v22.18.0", "pnpm": "10.15.0"}, "scripts": {"generate:component": "plop --cwd .", "generate:icons": "figma-export use-config && pnpm run lint:autofix", "generate:theme": "./scripts/theme/index.js && pnpm run format src/theme", "compile": "tsc", "test": "jest", "test:ci": "jest --ci --coverage", "lint": "biome check .", "lint:autofix": "biome check --write .", "format": "biome format --write", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@gorhom/bottom-sheet": "5.0.4", "@gorhom/portal": "1.0.14", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2", "@rn-primitives/avatar": "1.2.0", "@rn-primitives/slot": "1.2.0", "@testing-library/react-native": "12.8.1", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "nativewind": "4.1.23", "react": "19.0.0", "react-native": "0.79.6", "react-native-root-toast": "3.6.0", "react-native-safe-area-context": "5.4.0", "react-native-svg": "15.11.2", "tailwind-merge": "2.5.4"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/plugin-transform-export-namespace-from": "7.27.1", "@biomejs/biome": "2.0.6", "@figma-export/cli": "5.0.1", "@figma-export/output-components-as-svgr": "5.0.1", "@storybook/addon-docs": "9.1.3", "@storybook/addon-themes": "9.1.3", "@storybook/react-native-web-vite": "9.1.3", "@svgr/plugin-jsx": "8.1.0", "@svgr/plugin-svgo": "8.1.0", "@types/jest": "29.4.0", "@types/react": "19.0.10", "autoprefixer": "10.4.21", "babel-plugin-react-native-web": "0.19.11", "babel-preset-expo": "13.2.3", "jest": "29.7.0", "plop": "4.0.1", "react-dom": "19.0.0", "react-native-web": "0.19.13", "storybook": "9.1.3", "tailwindcss": "3.4.14", "typescript": "5.8.3", "vite": "7.1.3"}}