import React, { forwardRef, type PropsWithChildren, useMemo, useRef } from 'react';
import {
  BottomSheetBackdrop,
  type BottomSheetHandleProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import type { BottomSheetModalMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import { composeRefs } from '@radix-ui/react-compose-refs';
import * as Slot from '@rn-primitives/slot';
import { remapProps } from 'nativewind';
import { type GestureResponderEvent, Pressable, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import type { PressableRef, SlottablePressableProps } from '../primitives/types';
import { cn } from '../utils';

export * from '@gorhom/bottom-sheet';

const CustomisedBottomSheetModal = remapProps(BottomSheetModal, {
  className: 'style',
  containerClassName: 'containerStyle',
});

type RootContext = {
  bottomSheetModalRef: React.RefObject<BottomSheetModalMethods | null>;
};

const BottomSheetContext = React.createContext<RootContext | null>(null);

export const useBottomSheetContext = () => {
  const context = React.useContext(BottomSheetContext);

  if (!context) {
    throw new Error('BottomSheet compound components cannot be rendered outside the BottomSheet component');
  }
  return context;
};

type RootProps = PropsWithChildren;

export const Root: React.FC<RootProps> = ({ children }) => {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const value = useMemo(() => ({ bottomSheetModalRef }), [bottomSheetModalRef]);

  return (
    <BottomSheetContext.Provider value={value}>
      <BottomSheetModalProvider>{children}</BottomSheetModalProvider>
    </BottomSheetContext.Provider>
  );
};

export const Backdrop: React.FC<React.ComponentProps<typeof BottomSheetBackdrop>> = ({
  disappearsOnIndex,
  appearsOnIndex,
  ...props
}) => {
  return <BottomSheetBackdrop {...props} disappearsOnIndex={-1} appearsOnIndex={0} />;
};

export const DragIndicator = forwardRef<
  React.ElementRef<typeof View>,
  React.ComponentPropsWithoutRef<typeof View> & BottomSheetHandleProps
>(({ className, ...props }, ref) => {
  return <View ref={ref} className={cn('mx-auto w-10 h-1 mt-4 rounded bg-gray-200', className)} {...props} />;
});

export const Modal = forwardRef<
  React.ElementRef<typeof BottomSheetModal>,
  React.ComponentPropsWithoutRef<typeof BottomSheetModal>
>(
  (
    {
      children,
      enablePanDownToClose = true,
      index = 0,
      keyboardBehavior = 'interactive',
      keyboardBlurBehavior = 'restore',
      ...props
    },
    ref
  ) => {
    const { bottomSheetModalRef } = useBottomSheetContext();
    const refs = composeRefs(bottomSheetModalRef, ref);

    return (
      <CustomisedBottomSheetModal
        ref={refs}
        containerClassName="bg-overlay-subtle"
        backdropComponent={Backdrop}
        enablePanDownToClose={enablePanDownToClose}
        handleComponent={DragIndicator}
        index={index}
        keyboardBehavior={keyboardBehavior}
        keyboardBlurBehavior={keyboardBlurBehavior}
        {...props}
      >
        <BottomSheetView>
          <>{children}</>
        </BottomSheetView>
      </CustomisedBottomSheetModal>
    );
  }
);

export const Header = forwardRef<React.ElementRef<typeof View>, React.ComponentPropsWithoutRef<typeof View>>(
  ({ className, ...props }, ref) => {
    return (
      <View
        ref={ref}
        className={cn('flex flex-row items-center gap-4 border-b border-neutral-subtlest p-4', className)}
        {...props}
      />
    );
  }
);

export const Title = forwardRef<React.ElementRef<typeof Text>, React.ComponentPropsWithoutRef<typeof Text>>(
  ({ className, ...props }, ref) => (
    <Text
      role="heading"
      ref={ref}
      className={cn('text-neutral-bold text-base leading-6 font-medium', className)}
      {...props}
    />
  )
);

export const Content = forwardRef<React.ElementRef<typeof View>, React.ComponentPropsWithoutRef<typeof View>>(
  ({ className, children, ...props }, ref) => {
    return (
      <SafeAreaView edges={['bottom', 'left', 'right']}>
        <View ref={ref} className={cn('px-4', className)} {...props}>
          {children}
        </View>
      </SafeAreaView>
    );
  }
);

export const Trigger = React.forwardRef<PressableRef, SlottablePressableProps>(
  ({ asChild, onPress: onPressProp, disabled = false, ...props }, ref) => {
    const { bottomSheetModalRef } = useBottomSheetContext();
    const onPress = (ev: GestureResponderEvent) => {
      if (disabled) return;
      bottomSheetModalRef.current?.present();
      onPressProp?.(ev);
    };

    const Component = asChild ? Slot.Pressable : Pressable;

    return (
      <Component
        ref={ref}
        aria-disabled={disabled ?? undefined}
        role="button"
        onPress={onPress}
        disabled={disabled}
        {...props}
      />
    );
  }
);

export const Close = React.forwardRef<PressableRef, SlottablePressableProps>(
  ({ asChild, onPress: onPressProp, disabled = false, ...props }, ref) => {
    const { bottomSheetModalRef } = useBottomSheetContext();
    const onPress = (ev: GestureResponderEvent) => {
      if (disabled) return;
      bottomSheetModalRef.current?.close();
      onPressProp?.(ev);
    };

    const Component = asChild ? Slot.Pressable : Pressable;

    return (
      <Component
        ref={ref}
        aria-disabled={disabled ?? undefined}
        role="button"
        onPress={onPress}
        disabled={disabled}
        {...props}
      />
    );
  }
);
