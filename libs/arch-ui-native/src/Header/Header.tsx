import React, { type ComponentPropsWithoutRef } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';
import { ChevronLeftIcon } from '../Icons/solid';

export type HeaderRootProps = ComponentPropsWithoutRef<typeof View>;

function HeaderRoot(props: HeaderRootProps) {
  return <View className="flex flex-row items-center gap-2 p-2" {...props} />;
}

export type HeaderContentProps = ComponentPropsWithoutRef<typeof View>;

function HeaderContent(props: HeaderContentProps) {
  return <View className="flex flex-col flex-1" {...props} />;
}

export type HeaderLeftProps = ComponentPropsWithoutRef<typeof TouchableOpacity> & {
  icon?: React.ReactElement;
};

function HeaderLeft({ icon, ...props }: HeaderLeftProps) {
  return (
    <TouchableOpacity className="flex flex-row items-center grow-0" {...props}>
      {icon || <ChevronLeftIcon className="text-black" testID="default-header-back-icon" />}
      {props.children}
    </TouchableOpacity>
  );
}

export type HeaderTextProps = ComponentPropsWithoutRef<typeof Text>;

function HeaderTitle(props: HeaderTextProps) {
  return <Text numberOfLines={1} className="text-lg leading-6 font-semibold text-black" {...props} />;
}

function HeaderDescription(props: HeaderTextProps) {
  return <Text className="text-sm leading-5 font-normal text-neutral-subtle" {...props} />;
}

export {
  HeaderRoot as Root,
  HeaderContent as Content,
  HeaderLeft as Left,
  HeaderTitle as Title,
  HeaderDescription as Description,
};
