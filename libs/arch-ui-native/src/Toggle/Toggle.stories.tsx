import React, { type ComponentProps, useEffect, useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react-native-web-vite';
import { View } from 'react-native';
import { Toggle } from './Toggle';

const themes: ComponentProps<typeof Toggle>['theme'][] = ['indigo'];
const sizes: ComponentProps<typeof Toggle>['size'][] = ['small', 'large'];

const meta: Meta<typeof Toggle> = {
  component: Toggle,
  title: 'Toggle',
  decorators: [
    (Story) => (
      <View className="flex flex-row gap-x-4 items-center">
        <Story />
      </View>
    ),
  ],
};
export default meta;

type Story = StoryObj<typeof Toggle>;
const Template = ({ theme = 'indigo', size = 'small', ...props }: ComponentProps<typeof Toggle>) => {
  const [value, setValue] = useState(Boolean(props.value));

  useEffect(() => {
    setValue(Boolean(props.value));
  }, [props.value]);

  return <Toggle {...props} theme={theme} size={size} onValueChange={setValue} value={value} />;
};

export const Default: Story = { render: Template };
export const Disabled: Story = {
  render: Template,
  args: {
    disabled: true,
  },
};

export const Themes: Story = {
  render: (props) => (
    <>
      {themes.map((theme) => (
        <Template key={theme} theme={theme} {...props} />
      ))}
    </>
  ),
};

export const Size: Story = {
  render: (props) => (
    <>
      {sizes.map((size) => (
        <Template key={size} size={size} {...props} />
      ))}
    </>
  ),
};

export const All = () => (
  <View className="flex flex-col gap-8">
    {[false, true].map((value) => (
      <View key={`${value}`} className="flex flex-row gap-2">
        {themes.map((theme) => (
          <View key={`${value}-${theme}`} className="flex flex-col gap-2">
            {sizes.map((size) => (
              <View key={`${theme}-${value}-${size}`} className="flex flex-col gap-2 items-start">
                <Toggle size={size} value={value} />
              </View>
            ))}
          </View>
        ))}
      </View>
    ))}
  </View>
);
