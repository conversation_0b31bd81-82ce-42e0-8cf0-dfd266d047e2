{"name": "@shape-construction/react-image-markup", "version": "3.0.15", "private": true, "description": "Markup Image with ReactJS (customizable)", "main": "src/index.ts", "engines": {"node": "v22.18.0", "pnpm": "10.15.0"}, "scripts": {"start": "vite", "compile": "tsc --noEmit", "lint": "biome check .", "lint:autofix": "biome check --write .", "format": "biome format --write", "test": "jest", "test:ci": "jest --ci"}, "repository": {"type": "git", "url": "git://github.com/shape-construction/react-image-markup.git"}, "keywords": [], "authors": [{"name": "Lionix Team", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"@fortawesome/fontawesome-free": "^6.2.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@types/fabric": "^4.5.14", "@types/jest": "29.4.0", "@types/lodash.isequal": "4.5.8", "@types/node": "^18.11.18", "@types/react": "19.0.10", "@types/react-dom": "19.1.7", "@vitejs/plugin-react": "4.3.3", "buffer": "6.0.3", "canvas": "3.2.0", "fabric": "^5.3.0", "jest": "29.4.3", "jest-environment-jsdom": "29.7.0", "lodash.isequal": "4.5.0", "react": "19.0.0", "react-dom": "19.0.0"}, "devDependencies": {"@biomejs/biome": "2.0.6", "ts-jest": "^29.4.0", "vite": "7.1.3", "typescript": "5.8.3"}}