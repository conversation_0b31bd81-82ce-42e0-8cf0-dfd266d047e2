{"name": "shape-frontend", "description": "Monorepo for all shape frontend apps and libs", "packageManager": "pnpm@10.15.0+sha256.84c19e788d7d7ee248e4a6b7152f8ebba0f4fe7380a5f443ca17d76c030052d2", "engines": {"node": "v22.18.0", "pnpm": "10.15.0"}, "scripts": {"compile": "turbo run compile", "lint": "turbo run lint", "lint:autofix": "turbo run lint:autofix", "test:ci": "turbo run test:ci", "build": "turbo run build", "build:storybook": "turbo run build:storybook", "commitlint:pr": "commitlint --from=master", "dependencies:sync": "manypkg fix"}, "dependencies": {"@biomejs/biome": "2.0.6", "@commitlint/cli": "19.8.1", "@manypkg/cli": "0.24.0", "husky": "9.1.7", "turbo": "2.5.4", "typescript": "5.8.3", "vercel": "44.2.0"}, "pnpm": {"overrides": {"jsonpath-plus@<=10.3.0": "10.3.0", "tough-cookie@<=4.1.3": "4.1.3"}, "onlyBuiltDependencies": ["@fortawesome/fontawesome-free", "@sentry/cli", "@swc/core", "@tailwindcss/oxide", "canvas", "core-js", "cypress", "es5-ext", "esbuild", "protobufjs", "puppeteer", "msw"]}}