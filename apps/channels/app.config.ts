import type { ExpoConfig } from 'expo/config';
import fs from 'fs';

type Variant = 'development' | 'staging' | 'production';

const info: Record<Variant, { name: string; package: string }> = {
  development: {
    name: 'Channels (development)',
    package: 'shapeconstruction.channels.development',
  },
  staging: {
    name: 'Channels (staging)',
    package: 'shapeconstruction.channels.staging',
  },
  production: {
    name: 'Channels',
    package: 'shapeconstruction.channels.production',
  },
};

export default (): ExpoConfig => {
  const ENVIRONMENT = (process.env.EXPO_PUBLIC_APP_ENV || 'development') as Variant;
  const information = info[ENVIRONMENT];

  let VERSION;
  try {
    VERSION = fs.readFileSync('.version-shape', 'utf-8').trim();
  } catch {
    VERSION = '0.0.1';
  }

  return {
    newArchEnabled: false,
    slug: 'channels',
    name: information.name,
    description: 'Channels is a workplace chat platform for the construction industry.',
    owner: 'shapeconstruction',
    version: VERSION,
    githubUrl: 'https://github.com/shape-construction/shape-frontend',
    orientation: 'portrait',
    userInterfaceStyle: 'light',
    icon: './assets/icons/icon.png',
    scheme: 'shape-channels',
    experiments: {
      typedRoutes: true,
      remoteBuildCache: {
        provider: 'eas',
      },
    },
    assetBundlePatterns: ['**/*'],
    ios: {
      jsEngine: 'hermes',
      entitlements: {
        'aps-environment': 'development',
      },
      bundleIdentifier: information.package,
      supportsTablet: true,
      config: {
        usesNonExemptEncryption: false,
      },
      infoPlist: {
        LSMinimumSystemVersion: '12.0',
        NSCameraUsageDescription: 'Allow $(PRODUCT_NAME) take and send photos or videos in channels from the app.',
        UIBackgroundModes: ['fetch'],
      },
      googleServicesFile: `./services/${ENVIRONMENT}/GoogleService-Info.plist`,
      privacyManifests: {
        NSPrivacyAccessedAPITypes: [
          {
            NSPrivacyAccessedAPIType: 'NSPrivacyAccessedAPICategoryDiskSpace',
            NSPrivacyAccessedAPITypeReasons: ['E174.1'],
          },
          {
            NSPrivacyAccessedAPIType: 'NSPrivacyAccessedAPICategorySystemBootTime',
            NSPrivacyAccessedAPITypeReasons: ['8FFB.1'],
          },
          {
            NSPrivacyAccessedAPIType: 'NSPrivacyAccessedAPICategoryFileTimestamp',
            NSPrivacyAccessedAPITypeReasons: ['DDA9.1'],
          },
          {
            NSPrivacyAccessedAPIType: 'NSPrivacyAccessedAPICategoryUserDefaults',
            NSPrivacyAccessedAPITypeReasons: ['CA92.1'],
          },
        ],
      },
    },
    android: {
      edgeToEdgeEnabled: true,
      jsEngine: 'hermes',
      adaptiveIcon: {
        foregroundImage: './assets/icons/android_foreground_icon.png',
        backgroundImage: './assets/icons/android_background_icon.png',
      },
      package: information.package,
      permissions: ['android.permission.CAMERA'],
      blockedPermissions: ['android.permission.USE_FULL_SCREEN_INTENT', 'android.permission.RECORD_AUDIO'],
      googleServicesFile: `./services/${ENVIRONMENT}/google-services.json`,
    },
    notification: {
      icon: './assets/icons/ic_notification.png',
      color: '#4338CA',
    },
    plugins: [
      // https://op-engineering.github.io/op-sqlite/docs/installation/#use_frameworks
      './expo-plugins/op-sqlite-plugin',
      './expo-plugins/withCustomConfig',
      'expo-router',
      'expo-secure-store',
      'expo-video',
      [
        'expo-splash-screen',
        {
          backgroundColor: '#ffffff',
          image: './assets/splashscreen/splashscreen-light.png',
          resizeMode: 'contain',
        },
      ],
      [
        'expo-location',
        {
          locationAlwaysAndWhenInUsePermission: 'Allow $(PRODUCT_NAME) to tag your photos and videos with location.',
        },
      ],
      [
        'expo-media-library',
        {
          photosPermission: 'Allow $(PRODUCT_NAME) to select images from your library to send in channels.',
          savePhotosPermission: 'Allow $(PRODUCT_NAME) to save channels media to your library.',
        },
      ],
      'react-native-compressor',
      '@react-native-firebase/app',
      '@react-native-firebase/messaging',
      '@react-native-google-signin/google-signin',
      [
        'expo-build-properties',
        {
          ios: {
            useFrameworks: 'static',
          },
          android: {
            extraMavenRepos: ['$rootDir/../../../../../node_modules/@notifee/react-native/android/libs'],
          },
        },
      ],

      [
        '@sentry/react-native/expo',
        {
          organization: 'shapeconstruction',
          project: 'channels',
        },
      ],
    ],
    extra: {
      eas: {
        projectId: '3e612fc9-c09d-4fbe-9d59-a457e034522e',
      },
    },
    updates: {
      url: 'https://u.expo.dev/3e612fc9-c09d-4fbe-9d59-a457e034522e',
    },
  };
};
