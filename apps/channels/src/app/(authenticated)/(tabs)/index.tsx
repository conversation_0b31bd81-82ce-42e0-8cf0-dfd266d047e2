import { type ComponentProps, useMemo, useState } from 'react';
import { useDebounceCallback } from '@react-hook/debounce';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import { ChannelPreview } from 'src/components/Channel/components/ChannelPreview/ChannelPreview';
import { PreviewAvatar } from 'src/components/Channel/components/PreviewAvatar/PreviewAvatar';
import { NoResults } from 'src/components/NoResults';
import { PushNotificationPermission } from 'src/components/PushNotificationPermission';
import RenderNull from 'src/components/RenderNull';
import { SearchInput } from 'src/components/SearchInput';
import type { ChannelFilters, ChannelSort } from 'stream-chat';
import { ChannelList } from 'stream-chat-expo';

const sort: ChannelSort = [{ pinned_at: -1 }, { last_message_at: -1 }, { updated_at: -1 }];

const getChannelsFilters = (searchChannelText: string): ChannelFilters => {
  const searchFilters: ChannelFilters = searchChannelText
    ? {
        $or: [
          { name: { $autocomplete: searchChannelText } },
          { 'member.user.name': { $autocomplete: searchChannelText } },
        ],
      }
    : {};

  return {
    $or: [
      {
        type: { $in: ['group', 'team'] },
        joined: true,
        ...searchFilters,
      },
      {
        type: 'messaging',
        joined: true,
        last_message_at: { $exists: true },
        ...searchFilters,
      },
      {
        type: 'personal',
        joined: true,
        ...searchFilters,
      },
    ],
  };
};

export const ChannelListScreen = () => {
  const [searchChannelText, setSearchChannelText] = useState('');
  const { t } = useTranslation();
  const filters: ChannelFilters = useMemo(() => getChannelsFilters(searchChannelText), [searchChannelText]);

  const onSearchChange: ComponentProps<typeof SearchInput>['onChangeText'] = useDebounceCallback(
    setSearchChannelText,
    250
  );

  const onChannelSelect: ComponentProps<typeof ChannelList>['onSelect'] = async (channel) => {
    router.push({
      pathname: '/channel/[channelId]',
      params: { channelId: channel.cid },
    });
  };

  return (
    <View className="flex-1 bg-surface">
      <View className="pt-4 pb-2 px-4">
        <SearchInput autoCapitalize="none" onChangeText={onSearchChange} placeholder={t('channelList.search')} />
      </View>
      <PushNotificationPermission.Banner />
      <ChannelList
        filters={filters}
        sort={sort}
        PreviewAvatar={PreviewAvatar}
        onSelect={onChannelSelect}
        HeaderNetworkDownIndicator={RenderNull}
        Preview={ChannelPreview}
        EmptyStateIndicator={() => <NoResults />}
      />
    </View>
  );
};

export default ChannelListScreen;
