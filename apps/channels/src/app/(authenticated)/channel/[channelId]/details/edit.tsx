import { useActionSheet } from '@expo/react-native-action-sheet';
import { usePatchApiProjectsProjectIdGroupsGroupId } from '@shape-construction/api/src/hooks';
import { Avatar, cn, Toast } from '@shape-construction/arch-ui-native';
import { ChevronLeftIcon, UsersIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Stack, useRouter } from 'expo-router';
import { DirectUpload } from 'libs/direct-upload/direct-upload';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { useMediaPickerActions } from 'src/components/MediaPicker/useMediaPickerActions';
import { useChannelContext } from 'stream-chat-expo';

export type EditChannelFormValues = {
  avatar?: string | null;
  name?: string;
};

export const EditChannelPage = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { channel } = useChannelContext();
  const editChannelMutation = usePatchApiProjectsProjectIdGroupsGroupId();
  const { control, formState, setValue, watch, handleSubmit } = useForm<EditChannelFormValues>({
    values: {
      avatar: channel?.data?.image,
      name: channel?.data?.name,
    },
  });

  const { showActionSheetWithOptions } = useActionSheet();
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { camera, gallery } = useMediaPickerActions();

  const hasPhoto = Boolean(watch('avatar'));

  const toggleMediaAttachment = () => {
    const actions = [
      {
        enabled: true,
        label: t('channel.details.edit.actions.takePhoto'),
        action: async () => {
          const result = await camera({ allowsMultipleSelection: false });
          if (!result.canceled) return result.assets[0].uri;
        },
      },
      {
        enabled: true,
        label: t('channel.details.edit.actions.choosePhoto'),
        action: async () => {
          const result = await gallery({ allowsMultipleSelection: false });
          if (!result.canceled) return result.assets[0].uri;
        },
      },
      { enabled: hasPhoto, label: t('channel.details.edit.actions.removePhoto'), action: () => null },
    ];
    const filteredActions = actions.filter(({ enabled }) => enabled);

    showActionSheetWithOptions(
      {
        containerStyle: { paddingBottom: bottomInset },
        cancelButtonIndex: filteredActions.length,
        destructiveButtonIndex: [filteredActions.length],
        options: [...filteredActions.map(({ label }) => label), t('actions.cancel')],
      },
      async (buttonIndex) => {
        if (Number.isInteger(buttonIndex)) {
          const imageUri = await actions.at(buttonIndex!)?.action();
          setValue('avatar', imageUri, { shouldDirty: true });
        }
      }
    );
  };

  const onEditGroup = handleSubmit(async (values) => {
    try {
      const avatar = values.avatar?.includes('file://') ? await DirectUpload(values.avatar, 'image') : undefined;

      return await editChannelMutation.mutateAsync(
        {
          projectId: channel?.data?.shape_project_id!,
          groupId: channel?.data?.shape_group_id!,
          data: {
            name: values.name,
            avatar,
          },
        },
        {
          onSuccess: () => {
            Toast.show(t('channel.details.edit.success'));
            router.dismissTo({
              pathname: '/channel/[channelId]/details',
              params: { channelId: channel.cid },
            });
          },
        }
      );
    } catch (error) {
      Toast.show(t('channel.details.edit.error'));
    }
  });

  return (
    <SafeAreaView>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: 'Edit group',
          headerLeft: () => (
            <TouchableOpacity accessibilityRole="button" onPress={router.back} className="flex flex-row items-center">
              <ChevronLeftIcon className="text-black" />
            </TouchableOpacity>
          ),
          headerRight: () => (
            <TouchableOpacity
              disabled={formState.isSubmitting || !formState.isDirty}
              accessibilityRole="button"
              onPress={onEditGroup}
              className="flex flex-row items-center disabled:opacity-50"
            >
              <Text className="text-sm leading-5 font-medium text-brand">{t('channel.details.edit.actions.save')}</Text>
            </TouchableOpacity>
          ),
        }}
      />
      <View className="p-4 flex flex-col gap-4">
        <Controller
          name="avatar"
          control={control}
          render={({ field }) => (
            <TouchableOpacity
              onPress={toggleMediaAttachment}
              accessibilityRole="button"
              className="flex items-center gap-2"
            >
              <Avatar.Root size="16" alt="group avatar">
                <Avatar.Image accessibilityRole="image" source={{ uri: field.value || undefined }} />
                <Avatar.Fallback className="bg-brand">
                  <Avatar.FallbackIcon Icon={UsersIcon} />
                </Avatar.Fallback>
              </Avatar.Root>
              <Text className="text-lg leading-6 font-semibold text-brand">
                {hasPhoto ? t('channel.details.edit.editPhoto') : t('channel.details.edit.addPhoto')}
              </Text>
            </TouchableOpacity>
          )}
        />
        <Controller
          name="name"
          control={control}
          render={({ field: { onChange, ...field }, fieldState }) => (
            <TextInput
              {...field}
              autoFocus
              data-invalid={fieldState.invalid}
              onChangeText={onChange}
              placeholder={t('channel.details.edit.namePlaceholder')}
              className={cn('w-full text-neutral-bold h-10 border-t border-b border-brand', {
                'border-danger': fieldState.invalid,
              })}
              maxLength={50}
            />
          )}
        />
      </View>
    </SafeAreaView>
  );
};

export default EditChannelPage;
