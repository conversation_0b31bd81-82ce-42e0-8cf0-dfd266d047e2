import { ChevronLeftIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Stack, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';

const styles = StyleSheet.create({
  contentStyle: {
    backgroundColor: 'white',
  },
});

export const InviteLayout = () => {
  const { t } = useTranslation();
  const router = useRouter();

  return (
    <Stack
      screenOptions={{
        contentStyle: styles.contentStyle,
        headerShown: true,
        headerTitleAlign: 'center',
        headerBackVisible: false,
        headerLeft: () => (
          <TouchableOpacity onPress={router.back} className="flex flex-row items-center">
            <ChevronLeftIcon className="text-black" />
          </TouchableOpacity>
        ),
        headerTitle: () => (
          <Text className="text-black text-base font-bold leading-normal">
            {t('channel.details.invite.actions.inviteMembers.title')}
          </Text>
        ),
      }}
    />
  );
};

export default InviteLayout;
