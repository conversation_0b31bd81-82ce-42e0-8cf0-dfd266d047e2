import { useEffect, useState } from 'react';
import { List, Toggle } from '@shape-construction/arch-ui-native';
import { ChevronLeftIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { useMutation } from '@tanstack/react-query';
import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { useChannelContext } from 'stream-chat-expo';

export const DetailsNotificationsSettingsScreen = () => {
  const { t } = useTranslation();
  const { channelId } = useLocalSearchParams<{ channelId?: string }>();
  const { channel } = useChannelContext();
  const router = useRouter();

  const toggleNotificationsMutation = useMutation({
    mutationFn: async ({ enabled }: { enabled: boolean }) => (enabled ? channel?.mute() : channel?.unmute()),
  });

  const [isNotificationsDisabled, setIsNotificationsDisabled] = useState(Boolean(channel?.muteStatus().muted));

  useEffect(() => {
    setIsNotificationsDisabled(Boolean(channel?.muteStatus().muted));
  }, [channel]);

  const handleToggleMuteNotifications = async (enabled: boolean) => {
    if (toggleNotificationsMutation.isPending) return;

    setIsNotificationsDisabled((current) => !current);

    try {
      await toggleNotificationsMutation.mutateAsync({ enabled });
    } catch {
      setIsNotificationsDisabled((current) => !current);
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('channel.details.notificationSettings.title'),
          headerTitleAlign: 'center',
          contentStyle: { backgroundColor: 'transparent' },
          headerLeft: () => (
            <TouchableOpacity accessibilityRole="button" onPress={router.back}>
              <ChevronLeftIcon className="text-black -left-2" />
            </TouchableOpacity>
          ),
        }}
      />

      <View className="flex-1 bg-white flex flex-col gap-8 mb-8">
        <List.Root>
          <List.Item asChild className="py-4">
            <View>
              <List.Content className="flex flex-col gap-1">
                <List.Title className="text-base leading-6 font-medium text-gray-900">
                  {t('channel.details.notificationSettings.actions.muteNotifications')}
                </List.Title>
              </List.Content>
              <Toggle
                testID={`${channelId}-toggle-mute-notification`}
                value={isNotificationsDisabled}
                onValueChange={handleToggleMuteNotifications}
              />
            </View>
          </List.Item>

          <List.Divider />
        </List.Root>
      </View>
    </>
  );
};

export default DetailsNotificationsSettingsScreen;
