import { useRef } from 'react';
import { Toast } from '@shape-construction/arch-ui-native';
import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import { Stack } from 'expo-router';
import * as Sharing from 'expo-sharing';
import { useTranslation } from 'react-i18next';
import { Alert, Platform, Text, TouchableOpacity, View } from 'react-native';
import QRCode from 'react-native-qrcode-svg';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useChannelShareLink } from 'src/components/ChannelInvite/hooks/useChannelShareLink';

const createTempImage = async (base64: string, name: string) => {
  const uri = `${FileSystem.cacheDirectory}qrcode-${name}.png`;

  await FileSystem.writeAsStringAsync(uri, base64, { encoding: FileSystem.EncodingType.Base64 });

  return uri;
};

export const QrCodePage = () => {
  const { t } = useTranslation();
  const { token, link } = useChannelShareLink();
  const qrCodeImageUrl = useRef<string | undefined>(undefined);

  const shareQrCode = async () => {
    if (!token) return;

    const fileUrl = await createTempImage(qrCodeImageUrl.current!, token!);
    Sharing.shareAsync(fileUrl);
  };

  const downloadQrCode = async () => {
    try {
      if (!token) return;
      const fileUrl = await createTempImage(qrCodeImageUrl.current!, token);

      await MediaLibrary.requestPermissionsAsync();
      await MediaLibrary.saveToLibraryAsync(fileUrl);
      Toast.show(t('channel.details.invite.qrcode.download.success.message'));
    } catch {
      Alert.alert(t('states.error'), t('channel.details.invite.qrcode.download.error.message'));
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitle: () => (
            <Text className="text-black text-base font-bold leading-normal">
              {t('channel.details.invite.qrcode.title')}
            </Text>
          ),
        }}
      />

      <SafeAreaView>
        <View className="h-full flex flex-col py-4 px-8">
          <View className="h-2/3 flex flex-col gap-6 justify-center items-center">
            <QRCode
              getRef={(c) => {
                // Warning: Delay the call to toDataURL to ensure the QRCode has fully rendered.
                // Without this delay, calling toDataURL too early on ios causes the app to crash.
                setTimeout(
                  () => {
                    c?.toDataURL((base64Image: string) => {
                      qrCodeImageUrl.current = base64Image;
                    });
                  },
                  Platform.OS === 'ios' ? 500 : 0
                );
              }}
              size={256}
              value={link}
            />
            <Text className="text-md leading-5 font-normal text-center text-neutral-subtle">
              {t('channel.details.invite.qrcode.description')}
            </Text>
          </View>

          <View className="flex-1 flex flex-col justify-end gap-2">
            <TouchableOpacity
              accessibilityRole="button"
              onPress={shareQrCode}
              className="w-full py-2.5 px-4 rounded bg-indigo-500"
            >
              <Text className="text-white text-center text-sm leading-5 font-medium">
                {t('channel.details.invite.qrcode.actions.share')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              accessibilityRole="button"
              onPress={downloadQrCode}
              className="w-full py-2.5 px-4 rounded border border-indigo-500"
            >
              <Text className="text-indigo-500 text-center text-sm leading-5 font-medium">
                {t('channel.details.invite.qrcode.actions.save')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </>
  );
};

export default QrCodePage;
