import { type ComponentProps, useCallback, useMemo, useState } from 'react';
import { useDebounceCallback } from '@react-hook/debounce';
import { usePatchApiProjectsProjectIdGroupsGroupIdMembers } from '@shape-construction/api/src/hooks';
import { List } from '@shape-construction/arch-ui-native';
import { CheckCircleIcon, ChevronLeftIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { useInfiniteQuery } from '@tanstack/react-query';
import { router, Stack } from 'expo-router';
import groupBy from 'lodash.groupby';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, type ListRenderItem, SectionList, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSession } from 'src/authentication/SessionProvider';
import { SelectedMembers } from 'src/components/Channel/components/SelectedMembers';
import { UserAvatar } from 'src/components/Channel/components/UserAvatar/UserAvatar';
import { NoResults } from 'src/components/NoResults';
import { SearchInput } from 'src/components/SearchInput';
import { usersInfinityQuery } from 'src/get-stream/queries/users';
import type { UserResponse } from 'stream-chat';
import { useChannelContext } from 'stream-chat-expo';

export const ChannelAddMembers = () => {
  const { user } = useSession();
  const { t } = useTranslation();
  const { channel } = useChannelContext();
  const [searchUserText, setSearchUserText] = useState<string>('');
  const [selectedContacts, setSelectedContacts] = useState<UserResponse[]>([]);
  const patchApiProjectsProjectIdGroupsGroupIdMembersMutation = usePatchApiProjectsProjectIdGroupsGroupIdMembers();
  const {
    data: users,
    fetchNextPage,
    isLoading: usersIsLoading,
    isFetchingNextPage,
  } = useInfiniteQuery({
    ...usersInfinityQuery(
      {
        banned: false,
        teams: { $in: [channel?.data?.team as string] },
        ...(searchUserText && { name: { $autocomplete: searchUserText } }),
      },
      { name: 1 },
      { limit: 30 }
    ),
    enabled: Boolean(user.channels?.streamChatUserId) && Boolean(channel?.data?.team),
    select: (response) =>
      response.pages.flatMap((paginated) => paginated.users).filter(({ id }) => id !== user.channels.streamChatUserId),
  });

  const groupedUsers = useMemo(() => {
    const alphabeticGrouped = groupBy(users, ({ name }) => name?.at(0));
    return Object.entries(alphabeticGrouped).map(([title, data]) => ({ title, data }));
  }, [users]);

  const selectedUsers = useMemo(
    () => users?.filter(({ id }) => selectedContacts.find((contact) => id === contact.id)) || [],
    [users, selectedContacts]
  );
  const hasContactsSelected = selectedContacts.length > 0;

  const isDefaultContactSelected = useCallback(
    (contactId: UserResponse['id']) => !!channel?.state.members[contactId],
    [channel?.state.members]
  );

  const isContactSelected = useCallback(
    (contactId: UserResponse['id']) => selectedContacts.find(({ id }) => id === contactId),
    [selectedContacts]
  );

  const onSelectContact = useCallback(
    (contact: UserResponse) => {
      setSelectedContacts((state) => {
        if (isContactSelected(contact.id)) return state.filter(({ id }) => id !== contact.id);
        return [...state, contact];
      });
    },
    [setSelectedContacts, isContactSelected]
  );

  const onSearchChange: ComponentProps<typeof SearchInput>['onChangeText'] = useDebounceCallback(
    setSearchUserText,
    250
  );

  const onRemoveContact = useCallback(
    (contactId: UserResponse['id']) => {
      setSelectedContacts((state) => state.filter(({ id }) => id !== contactId));
    },
    [setSelectedContacts]
  );

  const onChannelCreation = async () => {
    try {
      const selectedContactsIds = selectedContacts.map(({ shape_user_id }) => shape_user_id as string);
      await patchApiProjectsProjectIdGroupsGroupIdMembersMutation.mutateAsync({
        projectId: channel.data?.shape_project_id as string,
        groupId: channel.data?.shape_group_id as string,
        data: {
          add_user_ids: selectedContactsIds,
        },
      });
      router.dismissTo({
        pathname: '/channel/[channelId]',
        params: { channelId: channel.cid },
      });
    } catch {
      Alert.alert(t('states.error'), t('channel.details.addMembers.notifications.error'));
    }
  };

  const renderItem: ListRenderItem<UserResponse> | undefined = ({ item: contact }) => {
    const isSelected = isContactSelected(contact.id);
    const alreadySelected = isDefaultContactSelected(contact.id);

    if (alreadySelected)
      return (
        <>
          <List.Item disabled>
            <UserAvatar src={contact.image} size="lg" />
            <List.Content>
              <List.Title className="text-sm leading-none font-semibold text-neutral-bold">{contact.name}</List.Title>
              {typeof contact.email === 'string' && contact.email && (
                <Text
                  testID="mentions-item-email"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="shrink truncate text-sm font-normal text-neutral-subtle -mt-1"
                >
                  {contact.email}
                </Text>
              )}
              <List.SupportingText>{t('channel.details.addMembers.alreadySelected')}</List.SupportingText>
            </List.Content>
          </List.Item>

          <List.Divider />
        </>
      );

    return (
      <>
        <List.Item asChild className="px-4 pr-5 py-3 flex-row justify-between items-center border-b border-gray-200">
          <TouchableOpacity role="button" accessibilityRole="button" onPress={() => onSelectContact(contact)}>
            <UserAvatar src={contact.image} size="lg" />
            <List.Content>
              <List.Title className="text-sm leading-none font-semibold text-neutral-bold">{contact.name}</List.Title>
              {typeof contact.email === 'string' && contact.email && (
                <Text
                  testID="mentions-item-email"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="shrink truncate text-sm leading-none font-normal text-neutral-subtle"
                >
                  {contact.email}
                </Text>
              )}
            </List.Content>
            <View className="rounded-full border-input border-[1px] h-7 w-7 flex items-center justify-center">
              {isSelected && <CheckCircleIcon className="text-indigo-500 h-9 w-9" />}
            </View>
          </TouchableOpacity>
        </List.Item>

        <List.Divider />
      </>
    );
  };

  const renderUserList = () => {
    if (usersIsLoading) return <ActivityIndicator testID="loading-users" />;

    return (
      <SectionList
        accessibilityRole="list"
        sections={groupedUsers || []}
        extraData={selectedContacts}
        stickySectionHeadersEnabled
        keyExtractor={({ id }) => id}
        renderItem={renderItem}
        ListEmptyComponent={NoResults}
        renderSectionHeader={({ section: { title } }) => (
          <View className="w-full h-10 bg-neutral-subtle px-4 flex flex-col justify-end">
            <Text className="text-neutral-500 text-md font-bold pb-1">{title}</Text>
          </View>
        )}
        onEndReachedThreshold={20}
        onEndReached={() => fetchNextPage()}
        ListFooterComponent={() => (isFetchingNextPage ? <ActivityIndicator /> : null)}
      />
    );
  };

  return (
    <SafeAreaView edges={['bottom']} className="flex-1 bg-neutral-subtlest">
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('channel.details.addMembers.title'),
          headerTitleAlign: 'left',
          headerLeft: () => (
            <TouchableOpacity accessibilityRole="button" onPress={router.back}>
              <ChevronLeftIcon className="text-black" />
            </TouchableOpacity>
          ),

          headerRight: () => {
            if (!hasContactsSelected) return null;

            return (
              <TouchableOpacity
                role="button"
                accessibilityRole="button"
                aria-label={t('channel.details.addMembers.includeMembers')}
                onPress={onChannelCreation}
                className="px-2"
              >
                <Text className="text-sm leading-4 font-medium text-brand">{t('actions.done')}</Text>
              </TouchableOpacity>
            );
          },
        }}
      />

      <View className="flex flex-col my-2 gap-2">
        <View className="mx-4 py-1">
          <SearchInput onChangeText={onSearchChange} placeholder={t('channel.details.addMembers.search')} />
        </View>
        {hasContactsSelected ? <SelectedMembers members={selectedUsers} onRemoveContact={onRemoveContact} /> : null}
      </View>

      {renderUserList()}
    </SafeAreaView>
  );
};

export default ChannelAddMembers;
