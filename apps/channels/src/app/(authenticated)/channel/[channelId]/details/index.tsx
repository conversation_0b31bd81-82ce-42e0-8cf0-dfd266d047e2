import React, { useEffect, useState } from 'react';
import type { ChannelType } from '@shape-construction/api/channels/types/channel';
import {
  getApiProjectsProjectIdGroupsGroupIdChannelConfigurationQueryOptions,
  getApiProjectsProjectIdTeamsTeamIdChannelConfigurationQueryOptions,
  useDeleteApiProjectsProjectIdGroupsGroupId,
  usePatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration,
  usePatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration,
  useGetApiProjectsProjectIdTeamsTeamId as useProjectTeam,
} from '@shape-construction/api/src/hooks';
import type { TeamChannelConfigurationSchema } from '@shape-construction/api/src/types';
import { Badge, Header, List, Modal, Toast } from '@shape-construction/arch-ui-native';
import { ChevronRightIcon, UserGroupIcon, UserPlusIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { useInfiniteQuery, useQuery, useQueryClient } from '@tanstack/react-query';
import { Link, Stack, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Switch, Text, TouchableOpacity, View } from 'react-native';
import Animated, {
  interpolate,
  useAnimatedScrollHandler,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ChannelAvatar } from 'src/components/Channel/components/ChannelAvatar/ChannelAvatar';
import { GroupMemberOptionsBottomSheet } from 'src/components/Channel/components/GroupMemberOptionsBottomSheet/GroupMemberOptionsBottomSheet';
import { UserAvatar } from 'src/components/Channel/components/UserAvatar/UserAvatar';
import { useChannelMembersQueryOptions } from 'src/components/Channel/hooks/useChannelMembers';
import { useCustomChannelTitle } from 'src/components/Channel/hooks/useCustomChannelTitle';
import { useChannelContext, useChatContext } from 'stream-chat-expo';

const DIRECT_MESSAGE_CHANNEL_TYPES = ['messaging', 'personal'];

export const ChannelDetailsPage = () => {
  const { t } = useTranslation();
  const { client } = useChatContext();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { channel } = useChannelContext();
  const projectId = channel.data?.shape_project_id as string;
  const groupId = channel.data?.shape_group_id as string;
  const teamId = channel.data?.shape_team_id as string;
  const channelType = channel.type as ChannelType;

  const [isOpenDeleteConfirmationModal, setOpenDeleteConfirmationModal] = useState(false);
  const deleteGroupMutation = useDeleteApiProjectsProjectIdGroupsGroupId();
  const { data: projectTeam } = useProjectTeam(
    channel?.data?.shape_project_id as string,
    channel?.data?.shape_team_id as string
  );
  const channelMembersQueryOptions = useChannelMembersQueryOptions(channel);
  const {
    data: members,
    fetchNextPage,
    isFetching,
    isFetchingNextPage,
    hasNextPage,
  } = useInfiniteQuery(channelMembersQueryOptions);
  const memberMe = members?.find((member) => member.user_id === client.user!.id);
  const scrollY = useSharedValue(0);
  const scrollHandler = useAnimatedScrollHandler((event) => {
    scrollY.value = event.contentOffset.y;
  });
  const headerStyle = useAnimatedStyle(() => ({
    opacity: interpolate(scrollY.value, [0, 40], [0, 1]),
  }));
  const { channelTitle, projectTitle } = useCustomChannelTitle(channel);

  const teamConfigurationQueryOptions = getApiProjectsProjectIdTeamsTeamIdChannelConfigurationQueryOptions(
    channel?.data?.shape_project_id as string,
    channel?.data?.shape_team_id as string
  );
  const teamConfigurationMutation = usePatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration();

  const groupConfigurationQueryOptions = getApiProjectsProjectIdGroupsGroupIdChannelConfigurationQueryOptions(
    channel?.data?.shape_project_id as string,
    channel?.data?.shape_group_id as string
  );
  const groupConfigurationMutation = usePatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration();

  const { data: teamConfiguration, isLoading: isLoadingTeamConfiguration } = useQuery({
    ...teamConfigurationQueryOptions,
    enabled: Boolean(projectId && teamId && channelType === 'team'),
  });
  const { data: groupConfiguration, isLoading: isLoadingGroupConfiguration } = useQuery({
    ...groupConfigurationQueryOptions,
    enabled: Boolean(projectId && groupId && channelType === 'group'),
  });

  const channelConfiguration = teamConfiguration || groupConfiguration;
  const isLoadingChannelConfiguration = isLoadingTeamConfiguration || isLoadingGroupConfiguration;
  const showProjectTitle = Boolean(channelType === 'group' && projectTitle);
  const showDeleteGroupAction =
    (channelType === 'group' && memberMe?.is_moderator) || ['messaging', 'personal'].includes(channelType);
  const showEditGroupAction = channelType === 'group' && memberMe?.is_moderator;

  const toggleUploadOption = (value: boolean) => {
    if (channel.type === 'team') {
      if (!channel.data?.shape_project_id || !channel.data?.shape_team_id) return;

      queryClient.setQueryData<TeamChannelConfigurationSchema>(teamConfigurationQueryOptions.queryKey, (state) => {
        if (!state) return state;
        state.autoUploadDocuments = value;
        return state;
      });

      teamConfigurationMutation.mutate(
        {
          projectId: channel?.data?.shape_project_id,
          teamId: channel?.data?.shape_team_id,
          data: { auto_upload_documents: value },
        },
        {
          onSettled: () => {
            queryClient.invalidateQueries(teamConfigurationQueryOptions);
          },
        }
      );
    }
    if (channel.type === 'group') {
      if (!channel.data?.shape_project_id || !channel.data?.shape_group_id) return;
      queryClient.setQueryData<TeamChannelConfigurationSchema>(groupConfigurationQueryOptions.queryKey, (state) => {
        if (!state) return state;
        state.autoUploadDocuments = value;
        return state;
      });

      groupConfigurationMutation.mutate(
        {
          projectId: channel?.data?.shape_project_id,
          groupId: channel?.data?.shape_group_id,
          data: { auto_upload_documents: value },
        },
        {
          onSettled: () => {
            queryClient.invalidateQueries(groupConfigurationQueryOptions);
          },
        }
      );
    }
  };

  const onDeleteChannel = async () => {
    try {
      if (DIRECT_MESSAGE_CHANNEL_TYPES.includes(channel.type)) {
        await channel.delete();
      } else if (channel.type === 'group') {
        await deleteGroupMutation.mutateAsync({
          projectId: channel.data?.shape_project_id as string,
          groupId: channel.data?.shape_group_id as string,
        });
      }
    } catch (error) {
      Toast.show(t('channel.details.deleteConfirmation.error'));
    } finally {
      setOpenDeleteConfirmationModal(false);
    }
  };

  useEffect(() => {
    if (hasNextPage) fetchNextPage();
  }, [isFetching, hasNextPage, fetchNextPage]);

  if (isLoadingChannelConfiguration)
    return (
      <View className="h-full flex flex-col justify-center">
        <ActivityIndicator />
      </View>
    );

  return (
    <SafeAreaView edges={['bottom']} className="flex-1">
      <Stack.Screen
        options={{
          headerShown: true,
          headerTitleAlign: 'left',
          title: '',
          header: () => {
            return (
              <SafeAreaView edges={['top']}>
                <Header.Root>
                  <Header.Left onPress={router.back}>
                    <Animated.View style={[headerStyle]}>
                      <ChannelAvatar src={channel.data?.image as string} size="lg" />
                    </Animated.View>
                  </Header.Left>
                  <Header.Content>
                    <Animated.View className="w-full" style={[headerStyle, { flex: 1 }]}>
                      <Header.Title>{channelTitle}</Header.Title>
                      <Header.Description>
                        {t(`channel.details.channelType.${channelType}`)} •{' '}
                        {t('channel.details.members', { count: members?.length })}
                      </Header.Description>
                    </Animated.View>
                  </Header.Content>

                  {showEditGroupAction && (
                    <TouchableOpacity
                      accessibilityRole="button"
                      className="pl-2 pr-4"
                      onPress={() =>
                        router.push({
                          pathname: '/(authenticated)/channel/[channelId]/details/edit',
                          params: { channelId: channel.cid },
                        })
                      }
                    >
                      <Text className="text-sm leading-5 font-medium text-brand">
                        {t('channel.details.actions.edit')}
                      </Text>
                    </TouchableOpacity>
                  )}
                </Header.Root>
              </SafeAreaView>
            );
          },
        }}
      />
      <GroupMemberOptionsBottomSheet.Root>
        {memberMe?.is_moderator && <GroupMemberOptionsBottomSheet.Panel />}
        <Animated.FlatList
          onScroll={scrollHandler}
          scrollEventThrottle={1}
          testID="channel-details-list"
          accessibilityRole="list"
          data={members || []}
          className="h-full w-full"
          contentContainerClassName="flex flex-col items-center pt-4 gap-2"
          keyExtractor={({ user }) => user?.id!}
          ListHeaderComponent={
            <>
              {/* Channel info */}
              <View className="flex flex-col items-center gap-4 mb-4">
                <ChannelAvatar src={channel.data?.image as string} size="16" />
                <View className="flex flex-col items-center gap-2 w-64">
                  <Text
                    numberOfLines={2}
                    className="text-lg text-center leading-6 font-semibold text-gray-900 truncate"
                  >
                    {channelTitle}
                  </Text>
                  <Text className="text-xs leading-none font-normal text-gray-500/75">
                    {t(`channel.details.channelType.${channelType}`)} •{' '}
                    {t('channel.details.members', { count: members?.length })}
                  </Text>
                </View>
              </View>

              <List.Root>
                {/* Channel options */}
                {projectTeam?.availableActions.manageJoinToken && (
                  <>
                    <List.Divider />
                    <List.Item asChild>
                      <Link
                        accessibilityRole="link"
                        href={{
                          pathname: '/(authenticated)/channel/[channelId]/details/invite',
                          params: { channelId: channel.cid },
                        }}
                        asChild
                      >
                        <TouchableOpacity>
                          <List.Content>
                            <List.Title>{t('channel.details.invite.actions.inviteMembers.title')}</List.Title>
                          </List.Content>
                          <ChevronRightIcon className="text-gray-500" />
                        </TouchableOpacity>
                      </Link>
                    </List.Item>
                  </>
                )}
                {showProjectTitle && (
                  <>
                    <List.Divider />

                    <List.Item asChild>
                      <View>
                        <List.Content>
                          <Text className="text-sm font-normal text-neutral-subtle">{projectTitle}</Text>
                        </List.Content>
                      </View>
                    </List.Item>
                  </>
                )}
                <List.Divider />
                <List.Item asChild>
                  <Link
                    accessibilityRole="link"
                    href={{
                      pathname: '/(authenticated)/channel/[channelId]/details/notifications',
                      params: { channelId: channel.cid },
                    }}
                    asChild
                  >
                    <TouchableOpacity>
                      <List.Content>
                        <List.Title>{t('channel.details.invite.actions.notificationSettings')}</List.Title>
                      </List.Content>
                      <ChevronRightIcon className="text-neutral-subtle" />
                    </TouchableOpacity>
                  </Link>
                </List.Item>
                <List.Divider />
                {showDeleteGroupAction && (
                  <>
                    <List.Item asChild>
                      <TouchableOpacity accessibilityRole="button" onPress={() => setOpenDeleteConfirmationModal(true)}>
                        <List.Content>
                          <List.Title className="text-danger">{t('channel.details.actions.deleteGroup')}</List.Title>
                        </List.Content>
                      </TouchableOpacity>
                    </List.Item>
                    <List.Divider />
                  </>
                )}
                {/* Media section */}
                {['team', 'group'].includes(channelType) && (
                  <>
                    <List.Item>
                      <List.Content>
                        <List.Title>{t('channel.details.mediaSection.title')}</List.Title>
                        <List.SupportingText>{t('channel.details.mediaSection.subtitle')}</List.SupportingText>
                      </List.Content>

                      <Switch
                        aria-disabled={!channelConfiguration?.availableActions?.edit}
                        accessibilityLabel={t('channel.details.mediaSection.title')}
                        disabled={!channelConfiguration?.availableActions?.edit}
                        ios_backgroundColor="#e5e7eb"
                        onValueChange={toggleUploadOption}
                        thumbColor="#fff"
                        trackColor={{ false: '#e5e7eb', true: '#6366f1' }}
                        value={channelConfiguration?.autoUploadDocuments}
                      />
                    </List.Item>

                    <List.Divider />
                  </>
                )}
                <List.Item>
                  <List.Content>
                    <List.Title className="text-sm leading-5 font-medium">
                      {t('channel.details.members', { count: members?.length })}
                    </List.Title>
                  </List.Content>
                </List.Item>
                {channelType === 'group' && memberMe?.is_moderator && (
                  <>
                    <List.Divider />
                    <List.Item asChild>
                      <Link
                        accessibilityRole="link"
                        push
                        href={{
                          pathname: '/(authenticated)/channel/[channelId]/details/members/add',
                          params: { channelId: channel.cid },
                        }}
                        asChild
                      >
                        <TouchableOpacity>
                          <View className="bg-indigo-500 w-11 h-11 inline-flex flex-col justify-center items-center rounded-full">
                            <UserPlusIcon className="w-6 h-6 text-white" />
                          </View>
                          <List.Content>
                            <List.Title>{t('channel.details.invite.actions.addMembers')}</List.Title>
                          </List.Content>
                          <ChevronRightIcon className="text-gray-500" />
                        </TouchableOpacity>
                      </Link>
                    </List.Item>
                  </>
                )}
                {projectTeam?.availableActions.manageJoinToken && (
                  <>
                    <List.Divider />
                    <List.Item asChild>
                      <Link
                        accessibilityRole="link"
                        href={{
                          pathname: '/(authenticated)/channel/[channelId]/details/invite',
                          params: { channelId: channel.cid },
                        }}
                        asChild
                      >
                        <TouchableOpacity>
                          <View className="bg-indigo-500 w-11 h-11 inline-flex flex-col justify-center items-center rounded-full">
                            <UserGroupIcon className="text-white" />
                          </View>
                          <List.Content>
                            <List.Title>{t('channel.details.invite.actions.inviteMembers.title')}</List.Title>
                            <List.SupportingText>
                              {t('channel.details.invite.actions.inviteMembers.description')}
                            </List.SupportingText>
                          </List.Content>
                        </TouchableOpacity>
                      </Link>
                    </List.Item>
                  </>
                )}
                <List.Divider />
              </List.Root>
            </>
          }
          renderItem={({ item: member }) => {
            const isMemberMe = member.user_id === memberMe?.user_id;
            const isMemberModerator = member.is_moderator;
            const isAllowUpdateGroupMember = !isMemberMe && channelType === 'group' && memberMe?.is_moderator;
            const Component = isAllowUpdateGroupMember ? GroupMemberOptionsBottomSheet.Trigger : View;

            return (
              <React.Fragment key={member.user_id}>
                <List.Item asChild>
                  <Component selectedMember={member}>
                    <UserAvatar src={member.user?.image as string} size="lg" />
                    <List.Content>
                      <List.Title className="text-sm leading-none font-semibold text-neutral-bold">
                        {member.user?.name}
                      </List.Title>
                      {member.user?.email && (
                        <Text
                          testID="mentions-item-email"
                          numberOfLines={1}
                          ellipsizeMode="tail"
                          className="shrink truncate text-sm leading-none font-normal text-neutral-subtle"
                        >
                          {member.user.email}
                        </Text>
                      )}
                    </List.Content>

                    {isMemberModerator && (
                      <Badge.Root size="small" shape="rounded" theme="indigo">
                        <Badge.Text>{t('channel.details.moderator')}</Badge.Text>
                      </Badge.Root>
                    )}
                  </Component>
                </List.Item>

                <List.Divider />
              </React.Fragment>
            );
          }}
          ListFooterComponent={() => (isFetchingNextPage ? <ActivityIndicator /> : null)}
        />
        {/* Delete channel confirmation modal */}
        <Modal.Root open={isOpenDeleteConfirmationModal} onOpenChange={setOpenDeleteConfirmationModal}>
          <Modal.Portal>
            <Modal.Overlay />
            <Modal.Panel>
              <Modal.Header>
                <Modal.Title>{t('channel.details.deleteConfirmation.title')}</Modal.Title>
              </Modal.Header>
              <Modal.Content>
                <Text className="text-sm leading-5 font-normal text-neutral">
                  {t('channel.details.deleteConfirmation.subTitle')}
                </Text>
              </Modal.Content>
              <Modal.Footer>
                <View className="flex flex-row gap-4">
                  <TouchableOpacity accessibilityRole="button">
                    <Text
                      className="text-xs leading-4 font-medium"
                      onPress={() => setOpenDeleteConfirmationModal(false)}
                    >
                      {t('actions.cancel')}
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity accessibilityRole="button" onPress={onDeleteChannel}>
                    <Text className="text-xs leading-4 font-medium text-brand">{t('actions.yes')}</Text>
                  </TouchableOpacity>
                </View>
              </Modal.Footer>
            </Modal.Panel>
          </Modal.Portal>
        </Modal.Root>
      </GroupMemberOptionsBottomSheet.Root>
    </SafeAreaView>
  );
};

export default ChannelDetailsPage;
