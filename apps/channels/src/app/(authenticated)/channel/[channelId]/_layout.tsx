import { type ElementRef, useEffect, useRef, useState } from 'react';
import * as Sentry from '@sentry/react-native';
import {
  useGetApiProjects,
  usePostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments as useSaveAttachmentsToGallerMutation,
} from '@shape-construction/api/src/hooks';
import { BottomSheet, RadioGroup, Toast } from '@shape-construction/arch-ui-native';
import { CloudArrowUpIcon, XMarkIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Stack, useGlobalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, FlatList, Platform, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AutoCompleteSuggestionItem } from 'src/components/AutoCompleteInput/AutoCompleteSuggestionItem';
import { MessageAvatar } from 'src/components/Channel/components/MessageAvatar/MessageAvatar';
import { doDocCompressedUploadRequest } from 'src/components/Channel/utils/compressUploadMedia';
import { KeyboardCompatibleView } from 'src/components/KeyboardCompatibleView /KeyboardCompatibleView';
import { MessageDateSeparator } from 'src/components/Message/MessageDateSeparator/MessageDateSeparator';
import { InputReplyStateHeader } from 'src/components/Message/MessageInput/components/InputReplyStateHeader';
import { MessageContent } from 'src/components/Message/MessageSimple/MessageContent';
import { MessageSimple } from 'src/components/Message/MessageSimple/MessageSimple';
import { MessageStatus } from 'src/components/Message/MessageSimple/MessageStatus';
import { ReactionListBottom } from 'src/components/Message/MessageSimple/ReactionsList/ReactionListBottom';
import { supportedReactions } from 'src/components/Message/ReactionsSelector/reactionData';
import { SystemMessage } from 'src/components/Message/SystemMessage/SystemMessage';
import { messageActions } from 'src/components/Message/utils/messageActions';
import { MessageActionListItem } from 'src/components/MessageMenu/MessageActionListItem';
import { MessageReactionPicker } from 'src/components/MessageMenu/MessageReactionPicker';
import { MessageUserReactions } from 'src/components/MessageMenu/MessageUserReactions';
import RenderNull from 'src/components/RenderNull';
import { Reply } from 'src/components/Reply/Reply';
import { useMyMessageStreamTheme } from 'src/stream-theme';
import type { Channel as ChannelType } from 'stream-chat';
import { Channel, useChatContext } from 'stream-chat-expo';

export const ChannelLayout = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { channelId } = useGlobalSearchParams<{ channelId: string }>();
  const { client } = useChatContext();
  const [selectedMessage, setSelectedMessage] = useState<string>();

  const [channel, setChannel] = useState<ChannelType>();

  const { data: projects } = useGetApiProjects();
  const saveAttachmentsToGallery = useSaveAttachmentsToGallerMutation();
  const [selectedProject, setSelectedProject] = useState<string>();
  const projectSelectorRef = useRef<ElementRef<typeof BottomSheet.Modal>>(null);
  const myMessageThemeObject = useMyMessageStreamTheme();

  const onSaveAttachmentsToGallery = async () => {
    if (!channel || !selectedMessage || !selectedProject) return;

    saveAttachmentsToGallery.mutate(
      { messageId: selectedMessage, projectId: selectedProject },
      {
        onError: (e) => {
          // @ts-expect-error Remove this after pull request is merged https://github.com/kubb-labs/kubb/pull/1577
          Alert.alert(t('states.error'), e.response?.data.errorDescription);
        },
        onSuccess: () => {
          Toast.show(t('channel.messages.actions.saveToGallerySuccess'));
        },
        onSettled: () => {
          setSelectedProject(undefined);
          setSelectedMessage(undefined);
        },
      }
    );
  };

  useEffect(() => {
    const initializeChannel = async () => {
      let currentChannel: ChannelType | undefined;
      try {
        if (!channelId) return;
        const [type, id] = channelId.split(':');
        currentChannel = client.channel(type, id);
        setChannel(currentChannel);
        if (!currentChannel.initialized) await currentChannel.watch();
      } catch (error) {
        Sentry.captureException(error);
        throw error;
      }
      setChannel(currentChannel);
    };

    initializeChannel();

    return () => {
      setChannel(undefined);
    };
  }, [channelId, client]);

  useEffect(() => {
    const { unsubscribe } = client.on('channel.deleted', (event) => {
      const eventOwner = client.user?.id === event.user_id;
      if (eventOwner) Toast.show(t('channel.details.deleteConfirmation.success'));
      else Toast.show(t('channel.events.delete'));
      router.push('/');
    });

    return () => {
      unsubscribe();
    };
  }, [router]);

  if (!channel)
    return (
      <View className="h-full flex flex-col justify-center">
        <ActivityIndicator />
      </View>
    );

  return (
    // This bottom sheet root belongs to the bottom sheet modal used in MessageUserReactionsItem
    <BottomSheet.Root>
      <Channel
        channel={channel}
        messageSwipeToReplyHitSlop={{ left: -30, right: 0 }}
        AutoCompleteSuggestionItem={AutoCompleteSuggestionItem}
        // Adding this custom component to fix the issue with the keyboard on Android
        KeyboardCompatibleView={KeyboardCompatibleView}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : -Math.abs(bottomInset)}
        keyboardBehavior="height"
        doDocUploadRequest={doDocCompressedUploadRequest}
        UnreadMessagesNotification={RenderNull}
        EmptyStateIndicator={RenderNull}
        supportedReactions={supportedReactions}
        ReactionListBottom={ReactionListBottom}
        MessageUserReactions={MessageUserReactions}
        MessageFooter={RenderNull}
        NetworkDownIndicator={RenderNull}
        myMessageTheme={myMessageThemeObject}
        Reply={Reply}
        InputReplyStateHeader={InputReplyStateHeader}
        MessageSimple={MessageSimple}
        MessageStatus={MessageStatus}
        MessageContent={MessageContent}
        MessageAvatar={MessageAvatar}
        MessageSystem={SystemMessage}
        InlineDateSeparator={MessageDateSeparator}
        TypingIndicator={RenderNull}
        MessageReactionPicker={MessageReactionPicker}
        MessageActionListItem={MessageActionListItem}
        messageActions={(params) =>
          messageActions({
            ...params,
            saveToGallery: {
              action: () => {
                params.dismissOverlay();
                setSelectedMessage(params.message.id);
                projectSelectorRef.current?.present();
              },
              actionType: 'copyMessage',
              icon: <CloudArrowUpIcon className="text-gray-800" />,
              title: t('channel.messages.actions.saveToGallery'),
            },
            deleteMessage: {
              ...params.deleteMessage,
              titleStyle: { color: 'red' },
            },
          })
        }
      >
        <Stack screenOptions={{ headerShown: false, contentStyle: { backgroundColor: 'white' } }} />

        <BottomSheet.Root>
          <BottomSheet.Modal ref={projectSelectorRef} onDismiss={onSaveAttachmentsToGallery}>
            <BottomSheet.Header>
              <BottomSheet.Close>
                <XMarkIcon className="text-gray-800" />
              </BottomSheet.Close>
              <BottomSheet.Title>{t('channel.projectSelect')}</BottomSheet.Title>
            </BottomSheet.Header>
            <BottomSheet.Content className="max-h-[400]">
              <RadioGroup.Root value={selectedProject} onValueChange={setSelectedProject}>
                <FlatList
                  className="w-full"
                  data={projects}
                  contentContainerClassName="py-4 gap-6"
                  renderItem={({ item: project }) => {
                    return (
                      <RadioGroup.Item key={project.id} aria-labelledby={project.title} value={project.id}>
                        <View className="flex-1">
                          <RadioGroup.Label>{project.title}</RadioGroup.Label>
                        </View>
                      </RadioGroup.Item>
                    );
                  }}
                />
              </RadioGroup.Root>
            </BottomSheet.Content>
          </BottomSheet.Modal>
        </BottomSheet.Root>
      </Channel>
    </BottomSheet.Root>
  );
};
export default ChannelLayout;
