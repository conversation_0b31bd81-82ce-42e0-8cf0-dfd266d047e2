import { Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ChannelGroupCreationForm } from 'src/components/Channel/hooks/useChannelGroupCreation';

export const ChannelNewGroupLayout = () => {
  return (
    <SafeAreaView edges={['bottom']} className="flex-1">
      <ChannelGroupCreationForm>
        <Stack screenOptions={{ headerShown: true }} />
      </ChannelGroupCreationForm>
    </SafeAreaView>
  );
};
export default ChannelNewGroupLayout;
