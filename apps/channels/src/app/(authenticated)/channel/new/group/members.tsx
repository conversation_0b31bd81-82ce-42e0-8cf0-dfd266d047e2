import { type ComponentProps, useCallback, useMemo, useState } from 'react';
import { useDebounceCallback } from '@react-hook/debounce';
import { useGetApiProjectsProjectId } from '@shape-construction/api/src/hooks';
import { List } from '@shape-construction/arch-ui-native';
import { CheckCircleIcon, ChevronLeftIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { useInfiniteQuery } from '@tanstack/react-query';
import { Link, Stack, useRouter } from 'expo-router';
import groupBy from 'lodash.groupby';
import { useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, type ListRenderItem, SectionList, Text, TouchableOpacity, View } from 'react-native';
import { useSession } from 'src/authentication/SessionProvider';
import { SelectedMembers } from 'src/components/Channel/components/SelectedMembers';
import { UserAvatar } from 'src/components/Channel/components/UserAvatar/UserAvatar';
import { useChannelGroupFormContext } from 'src/components/Channel/hooks/useChannelGroupCreation';
import { NoResults } from 'src/components/NoResults';
import { SearchInput } from 'src/components/SearchInput';
import { usersInfinityQuery } from 'src/get-stream/queries/users';
import type { UserResponse } from 'stream-chat';

export const ChannelNewGroupMembers = () => {
  const { user } = useSession();
  const { t } = useTranslation();
  const router = useRouter();
  const { control, getValues } = useChannelGroupFormContext();
  const {
    fields: selectedMembers,
    append,
    remove,
  } = useFieldArray({
    keyName: '_id',
    control,
    name: 'members',
  });
  const [searchUserText, setSearchUserText] = useState<string>('');
  const { data: project, isLoading: isProjectLoading } = useGetApiProjectsProjectId(getValues('project'));
  const teamId = project?.channels.streamChatTeam;
  const {
    data: users,
    fetchNextPage,
    isLoading,
    isFetchingNextPage,
  } = useInfiniteQuery({
    enabled: Boolean(user.channels!.streamChatUserId) && Boolean(teamId),
    ...usersInfinityQuery(
      {
        banned: false,
        teams: { $in: [teamId!] },
        ...(searchUserText && { name: { $autocomplete: searchUserText } }),
      },
      { name: 1 },
      { limit: 30 }
    ),
    select: (response) =>
      response.pages.flatMap((paginated) => paginated.users).filter(({ id }) => id !== user.channels.streamChatUserId),
  });

  const groupedUsers = useMemo(() => {
    const alphabeticGrouped = groupBy(users, ({ name }) => name?.at(0));
    return Object.entries(alphabeticGrouped).map(([title, data]) => ({ title, data }));
  }, [users]);

  const hasContactsSelected = selectedMembers.length > 0;

  const isContactSelected = useCallback(
    (contactId: UserResponse['id']) => {
      return selectedMembers.find(({ id }) => id === contactId);
    },
    [selectedMembers]
  );

  const onSearchChange: ComponentProps<typeof SearchInput>['onChangeText'] = useDebounceCallback(
    setSearchUserText,
    250
  );

  const renderItem: ListRenderItem<UserResponse> | undefined = ({ item: contact }) => {
    const isSelected = isContactSelected(contact.id);
    const indexOfSelected = selectedMembers.findIndex(({ id }) => id === contact.id);
    const onSelectHandler = isSelected ? () => remove(indexOfSelected) : () => append(contact);

    return (
      <>
        <List.Item asChild className="px-4 pr-5 py-3 flex-row justify-between items-center border-b border-gray-200">
          <TouchableOpacity role="button" accessibilityRole="button" onPress={onSelectHandler}>
            <UserAvatar src={contact.image} size="lg" />
            <List.Content>
              <List.Title className="text-sm leading-none font-semibold text-neutral-bold">{contact.name}</List.Title>
              {typeof contact.email === 'string' && contact.email && (
                <Text
                  testID="mentions-item-email"
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  className="shrink truncate text-sm leading-none font-normal text-neutral-subtle"
                >
                  {contact.email}
                </Text>
              )}
            </List.Content>
            <View className="rounded-full border-input border-[1px] h-7 w-7 flex items-center justify-center">
              {isSelected && <CheckCircleIcon className="text-indigo-500 h-9 w-9" />}
            </View>
          </TouchableOpacity>
        </List.Item>

        <List.Divider />
      </>
    );
  };

  const renderUserList = () => {
    if (isLoading || isProjectLoading) return <ActivityIndicator testID="loading-users" />;

    return (
      <SectionList
        accessibilityRole="list"
        sections={groupedUsers || []}
        extraData={selectedMembers}
        stickySectionHeadersEnabled
        keyExtractor={({ id }) => id}
        renderItem={renderItem}
        ListEmptyComponent={NoResults}
        renderSectionHeader={({ section: { title } }) => (
          <View className="w-full h-10 bg-neutral-subtle px-4 flex flex-col justify-end">
            <Text className="text-neutral-500 text-md font-bold pb-1">{title}</Text>
          </View>
        )}
        onEndReachedThreshold={20}
        onEndReached={() => fetchNextPage()}
        ListFooterComponent={() => (isFetchingNextPage ? <ActivityIndicator /> : null)}
      />
    );
  };

  return (
    <View className="flex-1 bg-neutral-subtlest">
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('channel.newGroup.membersStep.title'),
          headerTitleAlign: 'center',
          headerLeft: () => (
            <TouchableOpacity accessibilityRole="button" onPress={router.back}>
              <ChevronLeftIcon className="text-black -left-2" />
            </TouchableOpacity>
          ),
          headerRight: () => {
            return (
              <Link
                accessibilityRole="link"
                disabled={!hasContactsSelected}
                href={{ pathname: '/channel/new/group/info' }}
                className="disabled:opacity-50"
                asChild
              >
                <TouchableOpacity>
                  <Text className="text-sm leading-4 font-medium text-brand">{t('actions.next')}</Text>
                </TouchableOpacity>
              </Link>
            );
          },
        }}
      />
      <View className="flex flex-col my-2 gap-2">
        <View className="mx-4 py-1">
          <SearchInput onChangeText={onSearchChange} placeholder={t('channel.newGroup.membersStep.search')} />
        </View>
        {hasContactsSelected ? (
          <View className="flex flex-col gap-2">
            <View className="flex justify-end w-full bg-gray-100 px-4">
              <Text className="text-sm leading-5 font-bold pt-3 pb-1">
                {t('channel.newGroup.membersStep.membersCount', { count: selectedMembers.length })}
              </Text>
            </View>

            {hasContactsSelected ? (
              <SelectedMembers
                contentContainerClassName="px-2 flex flex-row gap-2"
                members={selectedMembers}
                onRemoveContact={(_, index) => remove(index)}
              />
            ) : null}
          </View>
        ) : null}
      </View>

      {renderUserList()}
    </View>
  );
};

export default ChannelNewGroupMembers;
