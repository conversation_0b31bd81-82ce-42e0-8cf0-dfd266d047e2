import { usePostApiProjectsProjectIdGroups } from '@shape-construction/api/src/hooks';
import { Avatar, Toast } from '@shape-construction/arch-ui-native';
import { CameraPlusIcon, ChevronLeftIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import * as ImagePicker from 'expo-image-picker';
import { router, Stack } from 'expo-router';
import { DirectUpload } from 'libs/direct-upload/direct-upload';
import { Controller, useFieldArray } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Alert, ScrollView, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { useChannelGroupFormContext } from 'src/components/Channel/hooks/useChannelGroupCreation';
import { UserSelectAvatar } from 'src/components/UserSelectAvatar/UserSelectAvatar';
import { useChatContext } from 'stream-chat-expo';

export const ChannelNewGroupInfo = () => {
  const { t } = useTranslation();
  const { client } = useChatContext();
  const { control, setValue, watch, handleSubmit, formState } = useChannelGroupFormContext();
  const createProjectGroup = usePostApiProjectsProjectIdGroups();
  const { fields: selectedMembers } = useFieldArray({
    keyName: '_id',
    control,
    name: 'members',
  });
  const avatar = watch('avatar');

  const pickImage = async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (permissionResult.granted === false) {
      Toast.show(t('permissions.mediaLibrary.denied'));
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: true,
    });

    if (!result.canceled) setValue('avatar', result.assets[0].uri);
  };

  const onChannelCreation = handleSubmit(async (values) => {
    try {
      const membersIds = values.members?.map(({ shape_user_id }) => shape_user_id as string) || [];
      const blobSignedId = values.avatar ? await DirectUpload(values.avatar, 'image') : undefined;

      return createProjectGroup.mutateAsync(
        {
          projectId: values.project,
          data: {
            name: values.name,
            avatar: blobSignedId,
            user_ids: membersIds,
          },
        },
        {
          onSuccess: async ({ channels }) => {
            const [channelType, channelId] = channels.channelCid.split(':');
            const conversation = client.channel(channelType, channelId);
            await conversation.watch();

            router.dismissTo({
              pathname: '/channel/[channelId]',
              params: { channelId: channels.channelCid },
            });
          },
        }
      );
    } catch {
      Alert.alert(t('states.error'), t('channel.newChannel.errorMessages.creatingChannelError'));
    }
  });

  return (
    <View className="flex-1 bg-neutral-subtlest">
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('channel.newGroup.infoStep.title'),
          headerTitleAlign: 'center',
          headerLeft: () => (
            <TouchableOpacity accessibilityRole="button" onPress={router.back}>
              <ChevronLeftIcon className="text-black -left-2" />
            </TouchableOpacity>
          ),
          headerRight: () => {
            return (
              <TouchableOpacity
                role="button"
                accessibilityRole="button"
                aria-label={t('actions.create')}
                onPress={onChannelCreation}
                className="disabled:opacity-50"
                disabled={formState.isSubmitting}
              >
                <Text className="text-sm leading-4 font-medium text-brand">{t('actions.create')}</Text>
              </TouchableOpacity>
            );
          },
        }}
      />
      <View className="flex flex-col py-4 gap-2">
        <View className="flex flex-row items-center px-4 gap-4">
          <TouchableOpacity
            accessibilityRole="imagebutton"
            aria-label={t('channel.newGroup.infoStep.uploadImage')}
            onPress={pickImage}
          >
            <Avatar.Root size="16" alt="">
              <Avatar.Image source={{ uri: avatar }} />
              <Avatar.Fallback className="bg-brand">
                <Avatar.FallbackIcon Icon={CameraPlusIcon} />
              </Avatar.Fallback>
            </Avatar.Root>
          </TouchableOpacity>
          <View className="flex-1 p-3 border-t border-b border-brand">
            <Controller
              name="name"
              control={control}
              render={({ field: { onChange, ...field } }) => (
                <TextInput
                  {...field}
                  maxLength={100}
                  onChangeText={onChange}
                  placeholder={t('channel.newGroup.infoStep.gorupNamePlaceholder')}
                />
              )}
            />
          </View>
        </View>
        <View className="flex flex-col py-4 gap-4">
          <View className="flex flex-col gap-2">
            <View className="flex justify-end w-full bg-gray-100 px-4">
              <Text className="text-sm leading-5 font-bold pt-3 pb-1">
                {t('channel.newGroup.infoStep.membersCount', { count: selectedMembers.length })}
              </Text>
            </View>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              aria-label={t('channel.newGroup.infoStep.selectedUsers')}
              contentContainerClassName="px-2 flex flex-row gap-2"
            >
              {selectedMembers?.map((member) => (
                <UserSelectAvatar key={member.id} user={member} />
              ))}
            </ScrollView>
          </View>
        </View>
      </View>
    </View>
  );
};

export default ChannelNewGroupInfo;
