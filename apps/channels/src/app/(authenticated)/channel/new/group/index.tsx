import React from 'react';
import { useGetApiProjects } from '@shape-construction/api/src/hooks';
import { RadioGroup } from '@shape-construction/arch-ui-native';
import { ChevronLeftIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Link, Stack, useRouter } from 'expo-router';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { useChannelGroupFormContext } from 'src/components/Channel/hooks/useChannelGroupCreation';

export const ChannelNewGroupProjectSelector = () => {
  const { data: projects } = useGetApiProjects();
  const { t } = useTranslation();
  const router = useRouter();
  const { control, watch } = useChannelGroupFormContext();
  const isProjectSelected = Boolean(watch('project'));

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          headerShadowVisible: false,
          title: t('channel.newGroup.projectStep.title'),
          headerTitleAlign: 'center',
          headerLeft: () => (
            <TouchableOpacity accessibilityRole="button" onPress={router.back}>
              <ChevronLeftIcon className="text-black -left-2" />
            </TouchableOpacity>
          ),
          headerRight: () => {
            return (
              <Link
                disabled={!isProjectSelected}
                accessibilityRole="link"
                push
                href={{ pathname: '/channel/new/group/members' }}
                asChild
                className="disabled:opacity-50"
              >
                <TouchableOpacity>
                  <Text className="text-sm leading-4 font-medium text-brand">{t('actions.next')}</Text>
                </TouchableOpacity>
              </Link>
            );
          },
        }}
      />
      <View className="flex-1 bg-white">
        <View className="flex flex-col mx-4 my-6 ">
          <ScrollView>
            <Controller
              name="project"
              control={control}
              render={({ field: { onChange, ...field } }) => (
                <RadioGroup.Root {...field} onValueChange={onChange} className="flex flex-col gap-6">
                  {projects?.map((project) => (
                    <RadioGroup.Item key={project.id} aria-labelledby={project.title} value={project.id}>
                      <View className="flex-1">
                        <RadioGroup.Label className="text-base leading-none font-normal text-neutral">
                          {project.title}
                        </RadioGroup.Label>
                      </View>
                    </RadioGroup.Item>
                  ))}
                </RadioGroup.Root>
              )}
            />
          </ScrollView>
        </View>
      </View>
    </>
  );
};

export default ChannelNewGroupProjectSelector;
