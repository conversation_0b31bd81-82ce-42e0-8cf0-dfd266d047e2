import { type ComponentProps, useCallback, useMemo, useState } from 'react';
import { useDebounceCallback } from '@react-hook/debounce';
import * as Sentry from '@sentry/react-native';
import { List } from '@shape-construction/arch-ui-native';
import { ChevronLeftIcon, ChevronRightIcon, UsersIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { useInfiniteQuery } from '@tanstack/react-query';
import { Link, Stack, useRouter } from 'expo-router';
import groupBy from 'lodash.groupby';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Alert, SectionList, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useSession } from 'src/authentication/SessionProvider';
import { UserAvatar } from 'src/components/Channel/components/UserAvatar/UserAvatar';
import { createMessagingChannel, createPersonalChannel } from 'src/components/Channel/utils/createChannel';
import { NoResults } from 'src/components/NoResults';
import { SearchInput } from 'src/components/SearchInput';
import { usersInfinityQuery } from 'src/get-stream/queries/users';
import type { UserResponse } from 'stream-chat';
import { useChatContext } from 'stream-chat-expo';

export const NewChannelScreen = () => {
  const router = useRouter();
  const { client } = useChatContext();
  const { user } = useSession();
  const channelsUser = user.channels!;
  const { t } = useTranslation();
  const [searchUserText, setSearchUserText] = useState<string>('');
  const streamUser = client.user!;
  const {
    data: users,
    fetchNextPage,
    isLoading,
    isFetchingNextPage,
  } = useInfiniteQuery({
    ...usersInfinityQuery(
      {
        banned: false,
        ...(searchUserText && { name: { $autocomplete: searchUserText } }),
      },
      { name: 1 },
      { limit: 30 }
    ),
    select: (response) =>
      response.pages.flatMap((paginated) => paginated.users).filter(({ id }) => id !== channelsUser.streamChatUserId),
  });

  const groupedUsers = useMemo(() => {
    const alphabeticGrouped = groupBy(users, ({ name }) => name?.at(0)?.toUpperCase());
    return Object.entries(alphabeticGrouped).map(([title, data]) => ({ title, data }));
  }, [users]);

  const onSelectContact = async (contact: UserResponse) => {
    try {
      const isPersonalChannel = contact.id === channelsUser.streamChatUserId;
      const conversation = await (isPersonalChannel
        ? createPersonalChannel(client, channelsUser)
        : createMessagingChannel(client, streamUser, contact));

      router.replace({
        pathname: '/channel/[channelId]',
        params: { channelId: conversation.cid },
      });
    } catch (e) {
      Sentry.captureException(e);
      Alert.alert(t('states.error'), t('channel.newChannel.errorMessages.creatingChannelError'));
    }
  };

  const onSearchChange: ComponentProps<typeof SearchInput>['onChangeText'] = useDebounceCallback(
    setSearchUserText,
    250
  );

  const renderItem = useCallback(
    ({ item: contact }: { item: UserResponse }) => {
      return (
        <>
          <List.Item asChild>
            <TouchableOpacity role="button" accessibilityRole="button" onPress={() => onSelectContact(contact)}>
              <UserAvatar src={contact.image} size="lg" />
              <List.Content>
                <List.Title className="text-sm leading-none font-semibold text-neutral-bold">{contact.name}</List.Title>
                {typeof contact.email === 'string' && contact.email && (
                  <Text
                    testID="mentions-item-email"
                    numberOfLines={1}
                    ellipsizeMode="tail"
                    className="shrink truncate text-sm leading-none font-normal text-neutral-subtle"
                  >
                    {contact.email}
                  </Text>
                )}
              </List.Content>
            </TouchableOpacity>
          </List.Item>

          <List.Divider />
        </>
      );
    },
    [streamUser, onSelectContact]
  );

  const renderMe = useCallback(
    () =>
      renderItem({
        item: {
          ...streamUser,
          name: `${streamUser.name} ${t('currentUserLabel')}`,
        },
      }),
    [streamUser, renderItem]
  );

  const renderUserList = () => {
    if (isLoading) return <ActivityIndicator testID="loading-users" />;

    return (
      <SectionList
        accessibilityRole="list"
        sections={groupedUsers || []}
        stickySectionHeadersEnabled
        keyExtractor={({ id }) => id}
        renderItem={renderItem}
        ListEmptyComponent={NoResults}
        ListHeaderComponent={renderMe}
        renderSectionHeader={({ section: { title } }) => (
          <View className="w-full h-10 bg-neutral-subtle px-4 flex flex-col justify-end">
            <Text className="text-neutral-500 text-md font-bold pb-1">{title}</Text>
          </View>
        )}
        onEndReachedThreshold={20}
        onEndReached={() => fetchNextPage()}
        ListFooterComponent={() => (isFetchingNextPage ? <ActivityIndicator /> : null)}
      />
    );
  };

  return (
    <SafeAreaView edges={['bottom']} className="flex-1 bg-neutral-subtlest">
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('channel.newChannel.title'),
          headerTitleAlign: 'center',
          headerLeft: () => (
            <TouchableOpacity onPress={router.back}>
              <ChevronLeftIcon className="text-black -left-2" />
            </TouchableOpacity>
          ),
        }}
      />
      <View className="flex flex-col my-2 gap-2">
        <View className="mx-4 py-1">
          <SearchInput onChangeText={onSearchChange} placeholder={t('channel.newChannel.searchPlaceholder')} />
        </View>
        <>
          <List.Divider />
          <List.Item asChild>
            <Link accessibilityRole="link" push href={{ pathname: '/channel/new/group' }} asChild>
              <TouchableOpacity>
                <View className="bg-indigo-500 w-11 h-11 inline-flex flex-col justify-center items-center rounded-full">
                  <UsersIcon className="text-white" />
                </View>
                <List.Content>
                  <List.Title>{t('channel.newChannel.newGroup')}</List.Title>
                </List.Content>
                <ChevronRightIcon className="text-black" />
              </TouchableOpacity>
            </Link>
          </List.Item>
        </>
      </View>

      {renderUserList()}
    </SafeAreaView>
  );
};

export default NewChannelScreen;
