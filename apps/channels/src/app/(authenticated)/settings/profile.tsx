import { useActionSheet } from '@expo/react-native-action-sheet';
import { getApiUsersMeQueryOptions, usePatchApiUsersMe } from '@shape-construction/api/src/hooks';
import { Avatar, BottomSheet, Toast } from '@shape-construction/arch-ui-native';
import {
  CameraPlusIcon,
  ChevronLeftIcon,
  LoadingIcon,
  QuestionMarkCircleIcon,
} from '@shape-construction/arch-ui-native/src/Icons/solid';
import { router, Stack } from 'expo-router';
import { DirectUpload } from 'libs/direct-upload/direct-upload';
import { Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Alert, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useSession } from 'src/authentication/SessionProvider';
import { useEditProfileForm } from 'src/components/Channel/hooks/useEditProfile';
import { useMediaPickerActions } from 'src/components/MediaPicker/useMediaPickerActions';
import { queryClient } from 'src/react-query/query-client';

export const ProfileScreen = () => {
  const { user } = useSession();
  const { mutateAsync: updateProfile, isPending } = usePatchApiUsersMe();
  const { t } = useTranslation();
  const { showActionSheetWithOptions } = useActionSheet();
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { camera, gallery } = useMediaPickerActions();
  const {
    control,
    getValues,
    handleSubmit,
    reset,
    setValue,
    formState: { isDirty, dirtyFields },
  } = useEditProfileForm({
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      avatarUrl: user?.avatarUrl,
    },
  });
  const avatarUrl = getValues('avatarUrl');

  const toggleMediaAttachment = () => {
    showActionSheetWithOptions(
      {
        containerStyle: { paddingBottom: bottomInset },
        cancelButtonIndex: 2,
        destructiveButtonIndex: 2,
        options: [t('channel.mediaOptions.camera'), t('channel.mediaOptions.photos'), t('actions.cancel')],
      },
      async (buttonIndex) => {
        const actions = [camera, gallery];
        if (Number.isInteger(buttonIndex)) {
          const result = await actions.at(buttonIndex!)?.({ allowsMultipleSelection: false });
          if (result && !result.canceled) setValue('avatarUrl', result.assets[0].uri, { shouldDirty: true });
        }
      }
    );
  };

  const saveProfile = handleSubmit(async (values) => {
    try {
      const newAvatar: Record<'avatar', string | undefined> = { avatar: undefined };

      if (dirtyFields.avatarUrl) {
        newAvatar.avatar = await DirectUpload(values.avatarUrl!, 'image');
      }

      await updateProfile(
        {
          data: {
            user: {
              first_name: values.firstName,
              last_name: values.lastName,
              email: values.email,

              ...(newAvatar.avatar ? newAvatar : {}),
            },
          },
        },
        {
          onSettled: () => {
            queryClient.invalidateQueries(getApiUsersMeQueryOptions());
          },
        }
      );

      reset(values);

      Toast.show(t('settings.profile.states.success'));
    } catch {
      Alert.alert(t('states.error'), t('settings.profile.states.error'));
    }
  });

  return (
    <View className="flex-1 bg-neutral-subtlest">
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('settings.profile.title'),
          headerTitleAlign: 'center',
          headerLeft: () => (
            <TouchableOpacity onPress={router.back}>
              <ChevronLeftIcon className="text-black -left-2" />
            </TouchableOpacity>
          ),
          headerRight: () =>
            isDirty ? (
              <>
                {isPending ? (
                  <View className="animate-spin">
                    <LoadingIcon width={20} height={20} className="text-neutral-bold" testID="profile-save-loading" />
                  </View>
                ) : (
                  <TouchableOpacity onPress={saveProfile}>
                    <Text className="text-sm leading-4 font-medium text-brand">{t('actions.save')}</Text>
                  </TouchableOpacity>
                )}
              </>
            ) : null,
        }}
      />
      <BottomSheet.Root>
        <View className="bg-white flex gap-4 h-full">
          <View className="flex flex-row px-4 py-2 items-center gap-2">
            <TouchableOpacity
              onPress={toggleMediaAttachment}
              accessibilityRole="button"
              className="flex items-center gap-2"
            >
              <Avatar.Root size="16" alt="">
                <Avatar.Image source={{ uri: avatarUrl || undefined }} />
                <Avatar.Fallback className="bg-brand">
                  <Avatar.FallbackIcon Icon={CameraPlusIcon} />
                </Avatar.Fallback>
              </Avatar.Root>
              <Text className="text-lg leading-6 font-semibold text-indigo-500">{t('settings.profile.edit')}</Text>
            </TouchableOpacity>

            <Text className="flex-1 w-full flex text-wrap text-sm leading-5 font-normal text-gray-400">
              {t('settings.profile.info')}
            </Text>
          </View>
          <View className="flex gap-1 px-4">
            <Text className="text-sm leading-5 font-medium text-neutral-bold">{t('settings.profile.firstName')}</Text>
            <Controller
              name="firstName"
              control={control}
              render={({ field: { onChange, ...field } }) => (
                <TextInput
                  {...field}
                  className="text-sm leading-5 font-normal text-neutral-bold px-3 py-4 border-t border-b border-t-gray-300 border-b-gray-300 focus:border-t-indigo-500 focus:border-b-indigo-500 disabled:border-none"
                  maxLength={100}
                  onChangeText={onChange}
                  placeholder={t('settings.profile.placeholder.firstName')}
                />
              )}
            />
          </View>
          <View className="flex gap-1 px-4">
            <Text className="text-sm leading-5 font-medium text-neutral-bold">{t('settings.profile.lastName')}</Text>
            <Controller
              name="lastName"
              control={control}
              render={({ field: { onChange, ...field } }) => (
                <TextInput
                  {...field}
                  className="text-sm leading-5 font-normal text-neutral-bold px-3 py-4 border-t border-b border-t-gray-300 border-b-gray-300 focus:border-t-indigo-500 focus:border-b-indigo-500 disabled:border-none"
                  maxLength={100}
                  onChangeText={onChange}
                  placeholder={t('settings.profile.placeholder.lastName')}
                />
              )}
            />
          </View>
          <View className="flex gap-1 p-4">
            <Text className="text-sm leading-5 font-medium  text-neutral-bold">{t('settings.profile.email')}</Text>
            <View className="flex flex-row justify-between items-center px-3 py-4">
              <Controller
                name="email"
                control={control}
                render={({ field: { onChange, ...field } }) => (
                  <TextInput
                    {...field}
                    aria-disabled
                    editable={false}
                    placeholder={t('settings.profile.placeholder.email')}
                    className="text-sm leading-5 font-normal text-neutral-bold aria-disabled:opacity-50"
                  />
                )}
              />
              <TouchableOpacity
                onPress={() =>
                  Alert.alert(t('settings.profile.emailInfo.title'), t('settings.profile.emailInfo.content'))
                }
              >
                <QuestionMarkCircleIcon className="text-neutral-subtle" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </BottomSheet.Root>
    </View>
  );
};

export default ProfileScreen;
