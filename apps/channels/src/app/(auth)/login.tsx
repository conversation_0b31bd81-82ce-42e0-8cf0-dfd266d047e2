import { yupResolver } from '@hookform/resolvers/yup';
import { getApiUsersMeQueryOptions, usePostApiLogin } from '@shape-construction/api/src/hooks';
import { XCircleIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Link, useGlobalSearchParams, useRouter } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Pressable, Text, TextInput, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ChannelsLogo from 'src/components/ChannelsLogo';
import { ERROR_CODES } from 'src/config/errorCodes';
import { queryClient } from 'src/react-query/query-client';
import * as yup from 'yup';

type FormValues = {
  password: string;
};

const schema = yup
  .object({
    password: yup.string().required('Password is required'),
  })
  .required();

export const Login = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });

  const { mutate, error, isPending } = usePostApiLogin();
  const { email } = useGlobalSearchParams<{ email: string }>();

  const onSubmit = (formData: FormValues) => {
    mutate(
      {
        data: {
          user: {
            email: email!,
            password: formData.password,
          },
        },
      },
      {
        onError: (errorData) => {
          if (errorData.response?.data.errorCode === ERROR_CODES.unconfirmed) {
            router.push({
              pathname: '/confirmEmail',
              params: { email },
            });
          }
        },
        onSuccess: (user) => {
          queryClient.setQueryData(getApiUsersMeQueryOptions().queryKey, user);
          router.replace('/');
        },
      }
    );
  };

  return (
    <SafeAreaView className="bg-neutral-subtlest flex-1">
      <View className="flex-1 py-24 px-5 flex flex-col">
        <View className="flex gap-y-4 items-center">
          <ChannelsLogo className="h-12 w-full" />
          <View className="flex flex-col gap-2">
            <Text className="leading-5 font-normal text-gray-500 text-center text-xs">{t('auth.subtitle')}</Text>
          </View>
        </View>
        {error && (
          <View className="flex flex-row gap-x-2 mt-8 p-3 items-center justify-center bg-red-50 border border-red-100 rounded-md">
            <XCircleIcon color="red" />
            <Text className="flex-1 text-red-700 text-wrap shrink-1">{error.response?.data.errorDescription}</Text>
          </View>
        )}

        <View className="flex gap-y-4 mt-7">
          <View>
            <View className="flex flex-row justify-between items-center">
              <Text className="mb-1 text-gray-700 text-md leading-5 font-medium">{t('auth.form.email')}</Text>
              <Link href="/auth" className="font-medium text-indigo-700">
                {t('auth.form.notYou')}
              </Link>
            </View>
            <TextInput
              aria-label={t('auth.form.email')}
              defaultValue={email}
              value={email!}
              editable={false}
              autoCapitalize="none"
              className="p-3 border border-neutral-subtle rounded-md text-gray-500"
            />
          </View>

          <View>
            <Text className="mb-1 text-gray-700 text-md leading-5 font-medium">{t('auth.form.password')}</Text>
            <Controller
              control={control}
              rules={{
                required: true,
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  aria-label={t('auth.form.password')}
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  secureTextEntry
                  className="p-3 border border-neutral-subtle rounded-md bg-neutral-white text-neutral placeholder:text-neutral-subtlest"
                />
              )}
              name="password"
            />
            {errors.password && <Text className="mt-2 text-red-500">{errors.password?.message}</Text>}
          </View>
        </View>

        <Pressable
          accessibilityRole="button"
          onPress={handleSubmit(onSubmit)}
          disabled={isPending}
          className="bg-indigo-500 active:bg-indigo-700 p-3 rounded-md mt-8"
        >
          <Text className="text-white text-center font-medium text-sm">{t('auth.form.controls.login')}</Text>
        </Pressable>

        <View className="mt-8">
          <ActivityIndicator animating={isPending} />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Login;
