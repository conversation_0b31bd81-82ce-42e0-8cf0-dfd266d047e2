import { yupResolver } from '@hookform/resolvers/yup';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { List } from '@shape-construction/arch-ui-native';
import { GoogleIcon, MicrosoftIcon } from '@shape-construction/arch-ui-native/src/Icons/social-icon';
import { XCircleIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Link } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Pressable, Text, TextInput, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ChannelsLogo from 'src/components/ChannelsLogo';
import { useProviderAuth } from 'src/components/LoginProviders/useProviderAuth';
import { environment } from 'src/config/environment';
import * as yup from 'yup';

GoogleSignin.configure({
  offlineAccess: true,
  webClientId: environment.GOOGLE_WEB_CLIENT_ID,
});

type FormValues = {
  email: string;
};

const schema = yup
  .object({
    email: yup.string().email('Must have a valid email').required('Email is required'),
  })
  .required();

export const Auth = () => {
  const { t } = useTranslation();
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });
  const { handleEmailPasswordSignIn, handleGoogleSignIn, handleMicrosoftSignIn, isPending, error } = useProviderAuth();

  const handleFormSubmit = handleSubmit(({ email }) => handleEmailPasswordSignIn(email));

  return (
    <SafeAreaView className="bg-neutral-subtlest flex-1">
      <View className="flex-1 py-24 px-5 flex flex-col">
        <View className="flex gap-y-4 items-center">
          <ChannelsLogo className="h-12 w-full" />
          <View className="flex flex-col gap-2">
            <Text className="leading-5 font-normal text-gray-500 text-center text-xs">{t('auth.subtitle')}</Text>
          </View>
        </View>
        {error && (
          <View className="my-4 flex flex-row gap-x-2 p-3 items-center justify-center bg-red-50 border border-red-100 rounded-md">
            <XCircleIcon color="red" />
            <Text className="flex-1 text-red-700 text-wrap shrink-1">{error}</Text>
          </View>
        )}

        <View className="gap-3">
          <View className="flex flex-col my-2">
            <Text className="mb-1 text-gray-700 text-md leading-5 font-medium">{t('auth.form.email')}</Text>
            <Controller
              control={control}
              rules={{
                required: true,
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  aria-label={t('auth.form.email')}
                  placeholder="<EMAIL>"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect={false}
                  className="p-3 border border-neutral-subtle rounded-md bg-neutral-white text-neutral placeholder:text-neutral-subtlest"
                  inputMode="email"
                  keyboardType="email-address"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  textContentType="emailAddress"
                  value={value}
                />
              )}
              name="email"
            />
            {errors.email && <Text className="mt-2 text-red-500">{errors.email?.message}</Text>}
          </View>

          <Pressable
            accessibilityRole="button"
            onPress={handleFormSubmit}
            disabled={isPending}
            className="bg-indigo-500 active:bg-indigo-700 p-3 rounded-md"
          >
            <Text className="text-white text-center font-medium text-sm">{t('auth.form.controls.continue')}</Text>
          </Pressable>
        </View>

        <View className="gap-4 mt-8">
          <View className="flex flex-row items-center gap-2 ">
            <List.Divider className="flex-1 opacity-100" />
            <Text className="text-sm leading-5 font-normal text-neutral-subtlest">{t('auth.form.controls.or')}</Text>
            <List.Divider className="flex-1 opacity-100" />
          </View>

          <TouchableOpacity
            accessibilityRole="button"
            onPress={handleGoogleSignIn}
            disabled={isPending}
            className="bg-neutral-white active:bg-neutral-white-pressed p-3 rounded-md  border flex-row justify-center items-center gap-2 border-neutral-subtle"
          >
            <GoogleIcon className="h-6 w-6" />
            <Text className="text-neutral text-center font-medium text-sm">
              {t('auth.form.controls.signInWithGoogle')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            accessibilityRole="button"
            onPress={handleMicrosoftSignIn}
            disabled={isPending}
            className="bg-neutral-white active:bg-neutral-white-pressed p-3 rounded-md border-neutral-subtle border flex-row justify-center items-center gap-2"
          >
            <MicrosoftIcon className="h-6 w-6" />
            <Text className="text-neutral text-center font-medium text-sm">
              {t('auth.form.controls.signInWithMicrosoft')}
            </Text>
          </TouchableOpacity>
          <Link href="/(auth)/settings/help-center/noAccount" className="mt-4 self-center font-medium text-indigo-700">
            {t('auth.form.controls.noAccount')}
          </Link>
          <Link href="/(auth)/settings/help-center" className="mt-4 self-center font-medium text-indigo-700">
            {t('auth.form.controls.needHelp')}
          </Link>
          <ActivityIndicator animating={isPending} />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default Auth;
