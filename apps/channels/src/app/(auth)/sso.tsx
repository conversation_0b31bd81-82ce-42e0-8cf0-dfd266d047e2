import { postApiAuthenticationMutationKey } from '@shape-construction/api/src/hooks';
import type { LoginAttemptEmailPasswordSchema } from '@shape-construction/api/src/types';
import { ArrowLeftIcon, XCircleIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { type MutationState, useMutationState } from '@tanstack/react-query';
import { Link, Redirect } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ActivityIndicator, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import ChannelsLogo from 'src/components/ChannelsLogo';
import { GoogleLoginButton } from 'src/components/LoginProviders/Google/GoogleLoginButton';
import { MicrosoftLoginButton } from 'src/components/LoginProviders/Microsoft/MicrosoftLoginButton';
import { useProviderAuth } from 'src/components/LoginProviders/useProviderAuth';

export const SSO = () => {
  const { t } = useTranslation();
  const { handleGoogleSignIn, handleMicrosoftSignIn, isPending, error } = useProviderAuth();
  const mutationState = useMutationState<MutationState<LoginAttemptEmailPasswordSchema>>({
    filters: { mutationKey: postApiAuthenticationMutationKey() },
  });
  const latestMutationState = mutationState.at(-1);
  const ssoStrategyEnabled = latestMutationState?.data?.emailPassword.ssoRequired;

  if (!ssoStrategyEnabled) return <Redirect href="/auth" />;

  return (
    <SafeAreaView className="bg-neutral-subtlest flex-1">
      <View className="flex-1 py-24 px-5 flex flex-col">
        <View className="flex gap-y-6 items-center">
          <ChannelsLogo className="h-12 w-full" />
          <View className="flex flex-col gap-2 justify-center items-center">
            <Text className="text-2xl leading-7 font-semibold text-neutral-dark">{t('auth.sso.title')}</Text>
            <Text className="text-sm leading-5 font-normal text-neutral-subtle text-center">
              {t('auth.sso.subtitle')}
            </Text>
          </View>
        </View>
        {error && (
          <View className="flex flex-row gap-x-2 p-3 items-center justify-center bg-red-50 border border-red-100 rounded-md">
            <XCircleIcon color="red" />
            <Text className="flex-1 text-red-700 text-wrap shrink-1">{error}</Text>
          </View>
        )}

        <View className="mt-8 flex flex-col gap-8  justify-center items-center">
          <View className="flex flex-col gap-4 w-full">
            <GoogleLoginButton onPress={handleGoogleSignIn} disabled={isPending} />
            <MicrosoftLoginButton onPress={handleMicrosoftSignIn} disabled={isPending} />
          </View>
          {isPending && <ActivityIndicator className="py-2" animating={isPending} testID="loading-indicator" />}
          <Link href={{ pathname: '/auth' }} asChild className="flex-row items-center gap-2">
            <TouchableOpacity accessibilityRole="button" className="flex-row items-center gap-2">
              <ArrowLeftIcon className="h-6 w-6 text-brand" />
              <Text className="text-sm leading-5 font-medium text-brand">{t('auth.sso.backTo')}</Text>
            </TouchableOpacity>
          </Link>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SSO;
