import { useActionSheet } from '@expo/react-native-action-sheet';
import {
  deleteApiStreamChannelsChannelTypeChannelIdMockHandler,
  getApiStreamChannelsChannelTypeChannelIdMockHandler,
  getApiStreamMembersMockHandler,
} from '@shape-construction/api/channels/factories-handler';
import {
  groupConfigurationAvailableActionsFactory,
  groupConfigurationFactory,
} from '@shape-construction/api/factories/groups';
import { projectFactory } from '@shape-construction/api/factories/projects';
import {
  teamConfigurationAvailableActionsFactory,
  teamConfigurationFactory,
} from '@shape-construction/api/factories/team';
import { teamAvailableActionsFactory, teamFactory } from '@shape-construction/api/factories/teams';
import { userChannelsFactory, userFactory } from '@shape-construction/api/factories/users';
import { factoryList } from '@shape-construction/api/factories/utils';
import {
  deleteApiProjectsProjectIdGroupsGroupIdMockHandler,
  getApiProjectsProjectIdGroupsGroupIdChannelConfigurationMockHandler,
  patchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMockHandler,
  patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler,
} from '@shape-construction/api/handlers-factories/projects/groups';
import {
  getApiProjectsProjectIdTeamsTeamIdChannelConfigurationMockHandler,
  patchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMockHandler,
} from '@shape-construction/api/handlers-factories/projects/teams';
import { getApiUsersMeMockHandler } from '@shape-construction/api/handlers-factories/users';
import type { TeamSchema, UserSchema } from '@shape-construction/api/src/types';
import { Toast } from '@shape-construction/arch-ui-native';
import { HttpResponse, http } from 'msw';
import { ChannelDetailsPage } from 'src/app/(authenticated)/channel/[channelId]/details';
import { channelFactory, channelMemberFactory, channelResponseFactory } from 'src/get-stream/test/factories/channel';
import { channelContextFactory } from 'src/get-stream/test/factories/chat-context';
import { userFactory as streamUserFactory } from 'src/get-stream/test/factories/user';
import { mockClient } from 'src/get-stream/test/mock';
import { server } from 'src/tests/mock-server';
import { fireEvent, renderRouter, screen, waitFor } from 'src/tests/test-utils';
import type { Channel, UserResponse } from 'stream-chat';

jest.spyOn(Toast, 'show').mockImplementation(jest.fn());
jest.mock('@expo/react-native-action-sheet', () => ({
  ...jest.requireActual('@expo/react-native-action-sheet'),
  useActionSheet: jest.fn(),
}));

const useActionSheetMock = useActionSheet as jest.Mock;

describe('ChannelDetailsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    useActionSheetMock.mockReturnValue({ showActionSheetWithOptions: jest.fn() });
  });

  it.each([
    ['team', 'channel.details.channelType.team'],
    ['messaging', 'channel.details.channelType.messaging'],
  ])('renders the %s channels information without projects information', async (channelType, expectedTranslation) => {
    const channel = channelFactory({
      cid: 'channel-1',
      name: 'Project X',
      type: channelType,
      team: projectFactory().channels.streamChatTeam,
    });

    renderRouter(
      { '/channel/[channelId]/details': ChannelDetailsPage },
      { initialUrl: '/channel/channel-1/details' },
      {
        stream: {
          channel: channelContextFactory({
            type: channel.type,
            id: channel.id,
            data: channel,
          }),
        },
      }
    );

    expect(await screen.findByText('Project X')).toBeOnTheScreen();
    expect(await screen.findByText(`${expectedTranslation} • channel.details.members`)).toBeOnTheScreen();
    expect(await screen.queryByText(projectFactory().title)).not.toBeOnTheScreen();
  });

  describe('when there is no group name header information', () => {
    it('renders the project short name with the members list ', async () => {
      const channelTitle = `${projectFactory().shortName} - User 1, User 2, User 3`;
      const client = mockClient({ user: streamUserFactory({ id: 'user-0' }) });
      const channel = channelContextFactory({
        type: 'group',
        data: channelFactory({
          cid: 'channel-1',
          name: undefined,
          type: 'group',
          team: projectFactory().channels.streamChatTeam,
        }),
        context: {
          state: {
            members: {
              'user-1': channelMemberFactory({
                user_id: 'user-1',
                user: userFactory({ id: 'user-1', name: 'User 1' }) as UserResponse,
              }),
              'user-2': channelMemberFactory({
                user_id: 'user-2',
                user: userFactory({ id: 'user-2', name: 'User 2' }) as UserResponse,
              }),
              'user-3': channelMemberFactory({
                user_id: 'user-3',
                user: userFactory({ id: 'user-3', name: 'User 3' }) as UserResponse,
              }),
            },
          },
        } as unknown as Channel,
      });

      renderRouter(
        { '/channel/[channelId]/details': ChannelDetailsPage },
        { initialUrl: '/channel/channel-1/details' },
        {
          stream: { client, channel },
        }
      );

      expect(await screen.findByText(channelTitle)).toBeOnTheScreen();
    });
  });

  it('renders the group channels information with the project short name and projects full title', async () => {
    const channelTitle = `${projectFactory().shortName} - Project X`;
    const expectedTranslation = 'channel.details.channelType.group • channel.details.members';
    const channel = channelFactory({
      cid: 'channel-1',
      name: 'Project X',
      type: 'group',
      team: projectFactory().channels.streamChatTeam,
    });

    renderRouter(
      { '/channel/[channelId]/details': ChannelDetailsPage },
      { initialUrl: '/channel/channel-1/details' },
      {
        stream: {
          channel: channelContextFactory({
            type: channel.type,
            id: channel.id,
            data: channel,
          }),
        },
      }
    );

    expect(await screen.findByText(channelTitle)).toBeOnTheScreen();
    expect(await screen.findByText(expectedTranslation)).toBeOnTheScreen();
    expect(await screen.findByText(projectFactory().title)).toBeOnTheScreen();
  });

  it('renders the members information in a sorted manner by role and alphabetically, with the current user displayed prominently', async () => {
    const members = [
      channelMemberFactory({
        user_id: 'user-4',
        is_moderator: true,
        user: {
          id: 'user-4',
          name: 'User 4',
          email: '<EMAIL>',
        },
      }),
      channelMemberFactory({
        user_id: 'user-2',
        user: {
          id: 'user-2',
          name: 'User 2',
          email: '<EMAIL>',
        },
      }),
      channelMemberFactory({
        user_id: 'user-10',
        is_moderator: true,
        user: { id: 'user-10', name: 'User 10' },
      }),
      channelMemberFactory({
        user_id: 'user-3',
        is_moderator: true,
        user: { id: 'user-3', name: 'User 3' },
      }),
    ];
    server.use(
      http.get('http://localhost/api/users/me', () =>
        HttpResponse.json<UserSchema>(userFactory({ channels: userChannelsFactory({ streamChatUserId: 'user-10' }) }))
      ),
      getApiStreamMembersMockHandler(() => ({ duration: '', members }))
    );

    renderRouter({ '/channel/[channelId]/details': ChannelDetailsPage }, { initialUrl: '/channel/channel-1/details' });

    const [member1, member2, member3, member4] = await screen.findAllByText(/^User /);

    expect(member1).toHaveTextContent('User 10');
    expect(member2).toHaveTextContent('User 3');
    expect(member3).toHaveTextContent('User 4');
    expect(member4).toHaveTextContent('User 2');
    expect(screen.getByText('<EMAIL>')).toBeOnTheScreen();
    expect(screen.getByText('<EMAIL>')).toBeOnTheScreen();
  });

  /**
   * Couldn't trigger the scroll event to test the infinite scroll
   */
  describe('by having the list virtualized', () => {
    it('only renders the first 10 members', async () => {
      server.use(
        getApiUsersMeMockHandler(() => userFactory({ channels: userChannelsFactory({ streamChatUserId: 'user-10' }) })),
        getApiStreamMembersMockHandler(({ request }) => {
          const payload = JSON.parse(new URL(request.url).searchParams.get('payload')!) satisfies {
            limit: string;
          };
          // End of pagination
          if (payload.limit === payload.offset) return { duration: '', members: [] };

          const newMembers = factoryList(channelMemberFactory, payload.limit, (index) => ({
            user_id: `${index}`,
            user: { id: `${index}`, name: `User name ${index}` },
          }));

          return { duration: '', members: newMembers };
        })
      );

      renderRouter(
        { '/channel/[channelId]/details': ChannelDetailsPage },
        { initialUrl: '/channel/channel-1/details' }
      );

      const firstMembersPage = await screen.findAllByText(/User name/i);
      expect(firstMembersPage[0]).toHaveTextContent('User name 0');
      expect(firstMembersPage[1]).toHaveTextContent('User name 1');
      expect(firstMembersPage[2]).toHaveTextContent('User name 10');
      expect(firstMembersPage[3]).toHaveTextContent('User name 11');
      expect(firstMembersPage[4]).toHaveTextContent('User name 12');
      expect(firstMembersPage[5]).toHaveTextContent('User name 13');
      expect(firstMembersPage[6]).toHaveTextContent('User name 14');
      expect(firstMembersPage[7]).toHaveTextContent('User name 15');
      expect(firstMembersPage[8]).toHaveTextContent('User name 16');
      expect(firstMembersPage[9]).toHaveTextContent('User name 17');
    });
  });

  describe.each([['team', 'group']])('when channel is type %s', (channelType) => {
    const channel = channelFactory({
      type: channelType,
      shape_project_id: 'project-1',
      shape_team_id: 'team-1',
      shape_group_id: 'group-1',
    });

    describe('and user has permission to toggle the upload option', () => {
      it('toggles the upload option', async () => {
        const teamConfiguration = teamConfigurationFactory({
          autoUploadDocuments: false,
          availableActions: teamConfigurationAvailableActionsFactory({ edit: true }),
        });
        const groupConfiguration = groupConfigurationFactory({
          autoUploadDocuments: false,
          availableActions: groupConfigurationAvailableActionsFactory({ edit: true }),
        });
        server.use(
          patchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMockHandler(async ({ request }) => {
            const body = await request.json();
            groupConfiguration.autoUploadDocuments = body.auto_upload_documents ?? false;

            return groupConfiguration;
          }),
          getApiProjectsProjectIdTeamsTeamIdChannelConfigurationMockHandler(async () => teamConfiguration),
          getApiProjectsProjectIdGroupsGroupIdChannelConfigurationMockHandler(async () => groupConfiguration)
        );
        renderRouter(
          { '/channel/[channelId]/details': ChannelDetailsPage },
          { initialUrl: '/channel/channel-1/details' },
          {
            stream: {
              channel: channelContextFactory({
                type: channel.type,
                id: channel.id,
                data: channel,
              }),
            },
          }
        );

        fireEvent(
          await screen.findByRole('switch', {
            disabled: false,
            name: 'channel.details.mediaSection.title',
          }),
          'onValueChange',
          true
        );

        await waitFor(() => {
          expect(
            screen.getByRole('switch', {
              name: 'channel.details.mediaSection.title',
            })
          ).toHaveProp('value', true);
        });
      });
    });

    describe('and the user does not have permission', () => {
      it('disables the toggle', async () => {
        server.use(
          patchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMockHandler(() =>
            teamConfigurationFactory({
              autoUploadDocuments: false,
              availableActions: teamConfigurationAvailableActionsFactory({
                edit: false,
              }),
            })
          ),
          patchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMockHandler(() =>
            groupConfigurationFactory({
              autoUploadDocuments: false,
              availableActions: groupConfigurationAvailableActionsFactory({
                edit: false,
              }),
            })
          )
        );

        renderRouter(
          { '/channel/[channelId]/details': ChannelDetailsPage },
          { initialUrl: '/channel/channel-1/details' },
          {
            stream: {
              channel: channelContextFactory({
                type: channel.type,
                id: channel.id,
                data: channel,
              }),
            },
          }
        );

        expect(
          await screen.findByRole('switch', {
            name: 'channel.details.mediaSection.title',
            disabled: true,
          })
        ).toBeOnTheScreen();
      });
    });
  });

  it('shows invite new members when has permission', async () => {
    server.use(
      http.get<{ projectId: string; teamId: string }, undefined, TeamSchema>(
        'http://localhost/api/projects/:projectId/teams/:teamId',
        () =>
          HttpResponse.json(
            teamFactory({
              availableActions: teamAvailableActionsFactory({
                manageJoinToken: true,
              }),
            })
          )
      )
    );

    renderRouter({ '/channel/[channelId]/details': ChannelDetailsPage }, { initialUrl: '/channel/channel-1/details' });

    expect(
      await screen.findAllByRole('link', {
        name: 'channel.details.invite.actions.inviteMembers.title',
      })
    ).toHaveLength(2);
  });

  it('shows invite new members when does not have permission', () => {
    server.use(
      http.get<{ projectId: string; teamId: string }, undefined, TeamSchema>(
        'http://localhost/api/projects/:projectId/teams/:teamId',
        () =>
          HttpResponse.json(
            teamFactory({
              availableActions: teamAvailableActionsFactory({
                manageJoinToken: false,
              }),
            })
          )
      )
    );

    renderRouter({ '/channel/[channelId]/details': ChannelDetailsPage }, { initialUrl: '/channel/channel-1/details' });

    expect(
      screen.queryAllByRole('link', {
        name: 'channel.details.invite.actions.inviteMembers.title',
      })
    ).toHaveLength(0);
  });

  describe('if user is a channel moderator', () => {
    it('show the add members action', async () => {
      const userMe = userFactory({
        channels: userChannelsFactory({ streamChatUserId: 'user-1' }),
      });
      const members = [
        channelMemberFactory({
          user_id: 'user-1',
          is_moderator: true,
          user: { id: 'user-1', name: 'User 1' },
        }),
      ];

      server.use(
        http.get('http://localhost/api/users/me', () => HttpResponse.json<UserSchema>(userMe)),
        getApiStreamMembersMockHandler(() => ({ duration: '', members }))
      );

      renderRouter(
        { '/channel/[channelId]/details': ChannelDetailsPage },
        { initialUrl: '/channel/channel-1/details' },
        {
          stream: {
            channel: channelContextFactory({ type: 'group' }),
          },
        }
      );

      expect(
        await screen.findByRole('link', {
          name: 'channel.details.invite.actions.addMembers',
        })
      ).toBeOnTheScreen();
    });
  });

  describe('if user is not a chanel moderator', () => {
    it('does not show the add members action', () => {
      const userMe = userFactory({
        channels: userChannelsFactory({ streamChatUserId: 'user-1' }),
      });
      const members = [
        channelMemberFactory({
          user_id: 'user-1',
          is_moderator: false,
          user: { id: 'user-1', name: 'User 1' },
        }),
      ];
      server.use(
        http.get('http://localhost/api/users/me', () => HttpResponse.json<UserSchema>(userMe)),
        getApiStreamMembersMockHandler(() => ({ duration: '', members }))
      );

      renderRouter(
        { '/channel/[channelId]/details': ChannelDetailsPage },
        { initialUrl: '/channel/channel-1/details' }
      );

      expect(
        screen.queryByRole('link', {
          name: 'channel.details.invite.actions.addMembers',
        })
      ).not.toBeOnTheScreen();
    });
  });

  describe('channel members actions and channel is group type', () => {
    const userMe = userFactory({
      channels: userChannelsFactory({ streamChatUserId: 'user-1' }),
    });

    describe('when user is a channel moderator', () => {
      const members = [
        channelMemberFactory({
          user_id: 'user-1',
          is_moderator: true,
          user: { id: 'user-1', name: 'User 1' },
        }),
        channelMemberFactory({
          user_id: 'user-2',
          is_moderator: false,
          user: { id: 'user-2', name: 'User 2', shape_user_id: '12' },
        }),
        channelMemberFactory({
          user_id: 'user-3',
          is_moderator: true,
          user: { id: 'user-3', name: 'User 3', shape_user_id: '13' },
        }),
      ];
      const channel = channelResponseFactory({
        channel: channelFactory({ cid: 'channel-1', type: 'group' }),
        members,
      });

      it('allows promoting a member to moderator', async () => {
        server.use(
          http.get('http://localhost/api/users/me', () => HttpResponse.json<UserSchema>(userMe)),
          getApiStreamChannelsChannelTypeChannelIdMockHandler(() => channel),
          getApiStreamMembersMockHandler(() => ({ duration: '', members })),
          patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler()
        );
        const { user } = renderRouter(
          { '/channel/[channelId]/details': ChannelDetailsPage },
          { initialUrl: '/channel/channel-1/details' },
          {
            user: userMe,
            stream: {
              channel: channelContextFactory({ type: 'group' }),
            },
          }
        );

        await user.press(await screen.findByRole('text', { name: /User 2/ }));
        await user.press(await screen.findByRole('text', { name: /groupMemberOptions.makeGroupModerator/ }));

        expect(Toast.show).toHaveBeenCalledWith(
          'channel.details.updateMembersRole.notifications.memberToModeratorSuccess'
        );
      });

      it('allows demoting a moderator to regular member', async () => {
        server.use(
          http.get('http://localhost/api/users/me', () => HttpResponse.json<UserSchema>(userMe)),
          getApiStreamChannelsChannelTypeChannelIdMockHandler(() => channel),
          getApiStreamMembersMockHandler(() => ({ duration: '', members })),
          patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler()
        );
        const { user } = renderRouter(
          { '/channel/[channelId]/details': ChannelDetailsPage },
          { initialUrl: '/channel/channel-1/details' },
          {
            user: userMe,
            stream: {
              channel: channelContextFactory({ type: 'group' }),
            },
          }
        );

        await user.press(await screen.findByRole('text', { name: /User 3/ }));
        await user.press(await screen.findByRole('text', { name: /groupMemberOptions.dismissModerator/ }));

        expect(Toast.show).toHaveBeenCalledWith(
          'channel.details.updateMembersRole.notifications.memberNoLongerModeratorSuccess'
        );
      });

      it('allows to remove members from the channel', async () => {
        server.use(
          http.get('http://localhost/api/users/me', () => HttpResponse.json<UserSchema>(userMe)),
          getApiStreamChannelsChannelTypeChannelIdMockHandler(() => channel),
          getApiStreamMembersMockHandler(() => ({ duration: '', members })),
          patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler()
        );
        const mockShowSheet = jest.fn((_, cb) => cb(0));
        useActionSheetMock.mockReturnValue({ showActionSheetWithOptions: mockShowSheet });
        const { user } = renderRouter(
          { '/channel/[channelId]/details': ChannelDetailsPage },
          { initialUrl: '/channel/channel-1/details' },
          {
            user: userMe,
            stream: {
              channel: channelContextFactory({ type: 'group' }),
            },
          }
        );

        await user.press(await screen.findByRole('text', { name: /User 2/ }));
        await user.press(await screen.findByRole('text', { name: /groupMemberOptions.removeFromGroup/ }));

        expect(Toast.show).toHaveBeenCalledWith('channel.details.removeMembers.notifications.removeMemberSuccess');
      });
    });

    describe('when user is not a channel moderator', () => {
      const members = [
        channelMemberFactory({
          user_id: 'user-1',
          is_moderator: false,
          user: { id: 'user-1', name: 'User 1' },
        }),
        channelMemberFactory({
          user_id: 'user-2',
          is_moderator: false,
          user: { id: 'user-2', name: 'User 2', shape_user_id: '12' },
        }),
        channelMemberFactory({
          user_id: 'user-3',
          is_moderator: false,
          user: { id: 'user-3', name: 'User 3', shape_user_id: '13' },
        }),
      ];
      const channel = channelResponseFactory({
        channel: channelFactory({ cid: 'channel-1', type: 'group' }),
        members,
      });

      it('does not allow to remove members and change member roles', async () => {
        server.use(
          http.get('http://localhost/api/users/me', () => HttpResponse.json<UserSchema>(userMe)),
          getApiStreamChannelsChannelTypeChannelIdMockHandler(() => channel),
          getApiStreamMembersMockHandler(() => ({ duration: '', members })),
          patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler()
        );
        renderRouter(
          { '/channel/[channelId]/details': ChannelDetailsPage },
          { initialUrl: '/channel/channel-1/details' },
          {
            user: userMe,
            stream: {
              channel: channelContextFactory({ type: 'group' }),
            },
          }
        );

        expect(screen.queryByRole('text', { name: /groupMemberOptions.removeFromGroup/ })).not.toBeOnTheScreen();
        expect(screen.queryByRole('text', { name: /groupMemberOptions.makeGroupModerator/ })).not.toBeOnTheScreen();
        expect(screen.queryByRole('text', { name: /groupMemberOptions.dismissModerator/ })).not.toBeOnTheScreen();
      });
    });
  });

  describe.each([['group'], ['messaging'], ['personal']])('when channel is type %s', (channelType) => {
    describe('and user is moderator', () => {
      it('closes the confirmation modal when deleting the channel', async () => {
        const members = [
          channelMemberFactory({
            user_id: 'user-1',
            is_moderator: true,
            user: { id: 'user-1', name: 'User Me' },
          }),
        ];
        server.use(
          getApiStreamMembersMockHandler(() => ({ duration: '', members })),
          deleteApiStreamChannelsChannelTypeChannelIdMockHandler(undefined, {
            status: 200,
          }),
          deleteApiProjectsProjectIdGroupsGroupIdMockHandler(undefined, {
            status: 200,
          })
        );
        const { user } = renderRouter(
          {
            '/(authenticated)/channel/[channelId]/details': ChannelDetailsPage,
          },
          { initialUrl: '/channel/channel-1/details' },
          {
            stream: {
              channel: channelContextFactory({ type: channelType }),
              client: mockClient({
                user: streamUserFactory({ id: 'user-1' }),
              }),
            },
          }
        );

        await user.press(
          await screen.findByRole('button', {
            name: 'channel.details.actions.deleteGroup',
          })
        );
        await user.press(await screen.findByRole('button', { name: 'actions.yes' }));

        expect(screen.queryByRole('dialog')).not.toBeOnTheScreen();
      });
    });
  });

  describe('when channel is type team and user is not moderator', () => {
    it('does not support to delete the channel', async () => {
      const members = [
        channelMemberFactory({
          user_id: 'user-1',
          is_moderator: true,
          user: { id: 'user-1', name: 'User 1' },
        }),
      ];
      server.use(
        http.get('http://localhost/api/users/me', () =>
          HttpResponse.json<UserSchema>(
            userFactory({
              channels: userChannelsFactory({ streamChatUserId: 'user-1' }),
            })
          )
        ),
        getApiStreamMembersMockHandler(() => ({ duration: '', members }))
      );
      renderRouter(
        { '/channel/[channelId]/details': ChannelDetailsPage },
        { initialUrl: '/channel/channel-1/details' },
        {
          stream: {
            channel: channelContextFactory({ type: 'group' }),
          },
        }
      );

      const deleteButtonQueryPromise = waitFor(() => {
        expect(
          screen.getByRole('button', {
            name: 'channels.details.actions.deleteGroup',
          })
        ).toBeVisible();
      });
      expect(deleteButtonQueryPromise).rejects.toThrow();
    });
  });
});
