import { useActionSheet } from '@expo/react-native-action-sheet';
import * as RouterModule from 'expo-router';
import { UploadPreviewScreen } from 'src/app/(authenticated)/channel/[channelId]/upload-preview';
import { useAttachmentPickerActions } from 'src/components/AttachmentPicker/hooks/useAttachmentPickerActions';
import { channelFactory } from 'src/get-stream/test/factories/channel';
import { channelContextFactory } from 'src/get-stream/test/factories/chat-context';
import { act, renderRouter, screen, userEvent, waitFor } from 'src/tests/test-utils';
import * as StreamChatExpoModule from 'stream-chat-expo';

const useMessageInputContextMocked = jest.spyOn(StreamChatExpoModule, 'useMessageInputContext');
const useActionSheetMocked = jest.mocked(useActionSheet);
const useAttachmentPickerActionsMocked = jest.mocked(useAttachmentPickerActions);

jest.mock('expo-video', () => ({
  VideoView: jest.fn(),
  useVideoPlayer: jest.fn(),
}));

jest.mock('stream-chat-expo', () => ({
  ...jest.requireActual('stream-chat-expo'),
  useMessageInputContext: jest.fn(),
}));

jest.mock('@expo/react-native-action-sheet', () => ({
  useActionSheet: jest.fn(() => ({
    showActionSheetWithOptions: jest.fn(),
  })),
}));

jest.mock('src/config/environment', () => ({
  environment: {
    ...jest.requireActual('src/config/environment').environment,
    FEATURE_FLAG_UPLOAD_PREVIEW_SCREEN: true,
  },
}));

jest.mock('src/components/AttachmentPicker/hooks/useAttachmentPickerActions', () => ({
  useAttachmentPickerActions: jest.fn(() => ({
    camera: jest.fn(),
    gallery: jest.fn(),
    documents: jest.fn(),
  })),
}));

describe('<UploadPreviewScreen />', () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('when user click on close button', () => {
    it('clears input and navigates to the channel screen', async () => {
      const resetInputMocked = jest.fn();
      const routerBackMocked = jest.fn();

      const useRouterMocked = jest.spyOn(RouterModule, 'useRouter').mockReturnValue({
        back: routerBackMocked,
        canDismiss: jest.fn(),
        canGoBack: jest.fn(),
        dismiss: jest.fn(),
        push: jest.fn(),
        replace: jest.fn(),
        dismissAll: jest.fn(),
        navigate: jest.fn(),
        setParams: jest.fn(),
        dismissTo: jest.fn(),
        reload: jest.fn(),
        prefetch: jest.fn(),
      });
      useMessageInputContextMocked.mockReturnValue({
        imageUploads: [],
        fileUploads: [],
        resetInput: resetInputMocked,
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);

      renderRouter(
        {
          '/(authenticated)/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
        },
        { initialUrl: '/(authenticated)/channel/channel-1/upload-preview' }
      );

      await userEvent.press(screen.getByRole('button', { name: 'close' }));

      expect(resetInputMocked).toHaveBeenCalled();
      expect(routerBackMocked).toHaveBeenCalled();

      useRouterMocked.mockRestore();
    });
  });

  it('lists the captured media', () => {
    useMessageInputContextMocked.mockReturnValue({
      imageUploads: [
        { id: 'image-1', file: { type: 'image' }, state: 'finished' },
        { id: 'image-2', file: { type: 'image' }, state: 'finished' },
        { id: 'image-3', file: { type: 'image' }, state: 'finished' },
      ],
      fileUploads: [
        { id: 'file-1', file: { type: 'video' }, state: 'finished' },
        { id: 'file-2', file: { type: 'file' }, state: 'finished' },
      ],
    } as Partial<StreamChatExpoModule.MessageInputContextValue>);

    renderRouter(
      {
        '/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
      },
      { initialUrl: '/channel/channel-1/upload-preview' }
    );

    expect(screen.getAllByRole('button', { name: /media/ })).toHaveLength(5);
  });

  it('sets the first media as selected by default', () => {
    useMessageInputContextMocked.mockReturnValue({
      imageUploads: [
        { id: 'file-1', file: { type: 'image', name: 'file 1', size: 100, uri: 'file-1' }, state: 'finished' },
        { id: 'file-2', file: { type: 'image', name: 'file 2', size: 100, uri: 'file-2' }, state: 'finished' },
        { id: 'file-3', file: { type: 'image', name: 'file 3', size: 100, uri: 'file-3' }, state: 'finished' },
      ],
      fileUploads: [],
    } as Partial<StreamChatExpoModule.MessageInputContextValue>);

    renderRouter(
      {
        '/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
      },
      { initialUrl: '/channel/channel-1/upload-preview' }
    );

    expect(screen.getAllByRole('button', { name: /media/ })[0]).toBeSelected();
    expect(screen.getByRole('image', { name: 'selected media' })).toHaveProp('alt', 'file 1');
  });

  describe('when user press on a media', () => {
    it('changes the selected the media', async () => {
      useMessageInputContextMocked.mockReturnValue({
        imageUploads: [
          { id: 'file-1', file: { type: 'image', name: 'file 1', size: 100, uri: 'file-1' }, state: 'finished' },
          { id: 'file-2', file: { type: 'image', name: 'file 2', size: 100, uri: 'file-2' }, state: 'finished' },
          { id: 'file-3', file: { type: 'image', name: 'file 3', size: 100, uri: 'file-3' }, state: 'finished' },
        ],
        fileUploads: [],
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);

      renderRouter(
        {
          '/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
        },
        { initialUrl: '/channel/channel-1/upload-preview' }
      );

      await userEvent.press(screen.getAllByRole('button', { name: /media/ })[1]);

      expect(screen.getAllByRole('button', { name: /media/ })[1]).toBeSelected();
      expect(screen.getByRole('image', { name: 'selected media' })).toHaveProp('alt', 'file 2');
    });
  });

  describe('when user press on send', () => {
    it('sends the message and navigates to the channel screen', async () => {
      const sendMessageMocked = jest.fn(() => Promise.resolve());
      useMessageInputContextMocked.mockReturnValue({
        resetInput: jest.fn(),
        imageUploads: [
          { id: 'file-1', file: { type: 'image', name: 'file 1', size: 100, uri: 'file-1' }, state: 'finished' },
        ],
        fileUploads: [],
        text: 'Hello, world!',
        sendMessage: sendMessageMocked,
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);

      renderRouter(
        {
          '/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
        },
        { initialUrl: '/channel/channel-1/upload-preview' }
      );

      await userEvent.press(await screen.findByRole('button', { name: 'send message' }));

      await waitFor(() => {
        expect(sendMessageMocked).toHaveBeenNthCalledWith(1, {
          customMessageData: { text: 'Hello, world!', attachments_caption: 'Hello, world!' },
        });
      });
      expect(screen).toHavePathname('/channel/messaging:channel-1');
    });
  });

  describe('when user press on media options', () => {
    describe('when attachment button is clicked', () => {
      it.each([
        ['gallery', 0],
        ['camera', 1],
        ['documents', 2],
      ])('upload from %s channels information without projects information', async (_option, index) => {
        jest.useFakeTimers();
        jest.spyOn(StreamChatExpoModule, 'useMessageInputContext').mockReturnValue({
          isValidMessage: () => true,
        } as Partial<StreamChatExpoModule.MessageInputContextValue>);

        useAttachmentPickerActionsMocked.mockReturnValue({
          camera: () => Promise.resolve({ canceled: false }),
          gallery: () => Promise.resolve({ canceled: false }),
          documents: () => Promise.resolve({ canceled: false }),
        } as ReturnType<typeof useAttachmentPickerActions>);

        useMessageInputContextMocked.mockReturnValue({
          imageUploads: [],
          fileUploads: [],
        } as Partial<StreamChatExpoModule.MessageInputContextValue>);

        const showActionSheetWithOptions = jest.fn();
        useActionSheetMocked.mockReturnValue({ showActionSheetWithOptions });

        renderRouter(
          {
            '/channel/[channelId]/upload-preview/': () => <UploadPreviewScreen />,
          },
          { initialUrl: '/channel/channel-1/upload-preview' }
        );

        await userEvent.press(await screen.findByRole('button', { name: 'attachment options' }));
        act(() => {
          showActionSheetWithOptions.mock.calls[0][1](index);
        });

        await waitFor(() => {
          expect(screen).toHavePathname('/channel/channel-1/upload-preview/');
        });
      });
    });
  });

  describe('when media is selected', () => {
    it('does not show the delete option when there is only one file', () => {
      useMessageInputContextMocked.mockReturnValue({
        imageUploads: [
          { id: 'image-1', file: { name: 'image 1', type: 'image', size: 100, uri: 'image-1' }, state: 'finished' },
        ],
        fileUploads: [],
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);

      renderRouter(
        {
          '/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
        },
        { initialUrl: '/channel/channel-1/upload-preview' }
      );

      expect(screen.queryByRole('button', { name: 'remove media' })).not.toBeOnTheScreen();
    });

    it('shows the delete option when there is more than two files', () => {
      useMessageInputContextMocked.mockReturnValue({
        imageUploads: [
          { id: 'image-1', file: { name: 'image 1', type: 'image', size: 100, uri: 'image-1' }, state: 'finished' },
          { id: 'image-2', file: { name: 'image 2', type: 'image', size: 100, uri: 'image-2' }, state: 'finished' },
        ],
        fileUploads: [],
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);

      renderRouter(
        {
          '/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
        },
        { initialUrl: '/channel/channel-1/upload-preview' }
      );

      expect(screen.getByRole('button', { name: 'remove media' })).toBeOnTheScreen();
    });

    it('triggers removeImage when the delete button is clicked on image type', async () => {
      const removeImageMocked = jest.fn();
      useMessageInputContextMocked.mockReturnValue({
        imageUploads: [
          { id: 'image-1', file: { name: 'image 1', type: 'image', size: 100, uri: 'image-1' }, state: 'finished' },
          { id: 'image-2', file: { name: 'image 2', type: 'image', size: 100, uri: 'image-2' }, state: 'finished' },
        ],
        fileUploads: [],
        removeImage: removeImageMocked,
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);

      renderRouter(
        {
          '/(authenticated)/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
        },
        { initialUrl: '/(authenticated)/channel/channel-1/upload-preview' }
      );

      // First select and then press to delete
      await userEvent.press(screen.getAllByRole('button', { name: /media/ })[1]);
      await userEvent.press(screen.getAllByRole('button', { name: /media/ })[1]);

      expect(removeImageMocked).toHaveBeenCalledWith('image-2');
    });

    it('triggers removeFile when the delete button is clicked on file type', async () => {
      const removeFileMocked = jest.fn();
      useMessageInputContextMocked.mockReturnValue({
        imageUploads: [],
        fileUploads: [
          { id: 'video-1', file: { name: 'video 1', type: 'video', size: 100, uri: 'video-1' }, state: 'finished' },
          { id: 'video-2', file: { name: 'video 2', type: 'video', size: 100, uri: 'video-2' }, state: 'finished' },
        ],
        removeFile: removeFileMocked,
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);

      renderRouter(
        {
          '/(authenticated)/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
        },
        { initialUrl: '/(authenticated)/channel/channel-1/upload-preview' }
      );

      // First select and then press to delete
      await userEvent.press(screen.getAllByRole('button', { name: /media/ })[1]);
      await userEvent.press(screen.getAllByRole('button', { name: /media/ })[1]);

      expect(removeFileMocked).toHaveBeenCalledWith('video-2');
    });
  });

  describe('when screen is unmount', () => {
    it('should reset the input', () => {
      const resetInputMocked = jest.fn();
      useMessageInputContextMocked.mockReturnValue({
        imageUploads: [],
        fileUploads: [],
        resetInput: resetInputMocked,
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);
      const triggerBeforeRemove = jest.fn();
      jest.spyOn(RouterModule, 'useNavigation').mockReturnValue({
        addListener: jest.fn((event, callback) => {
          if (event === 'beforeRemove') {
            triggerBeforeRemove.mockImplementation(callback);
          }
          return jest.fn(); // Mock the unsubscribe function
        }),
      });
      renderRouter(
        {
          '/(authenticated)/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
        },
        { initialUrl: '/(authenticated)/channel/channel-1/upload-preview' }
      );

      triggerBeforeRemove();

      expect(resetInputMocked).toHaveBeenCalled();
    });
  });

  describe('LocationPickerBottomSheet', () => {
    const renderLocationPicker = (channelType: string) => {
      renderRouter(
        {
          '/(authenticated)/channel/[channelId]/upload-preview': () => <UploadPreviewScreen />,
        },
        { initialUrl: '/(authenticated)/channel/channel-1/upload-preview' },
        {
          stream: {
            channel: channelContextFactory({ type: channelType, data: channelFactory({ type: channelType }) }),
          },
        }
      );
    };

    it('does not show the location picker if channel is a 1:1', () => {
      useMessageInputContextMocked.mockReturnValue({
        imageUploads: [],
        fileUploads: [],
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);

      renderLocationPicker('messaging');

      expect(screen.queryByTestId('bottomsheet-location-picker-trigger')).not.toBeOnTheScreen();
    });

    test.each([
      ['team', 'shows the location picker if channel is a type of team'],
      ['group', 'shows the location picker if channel is a type of group'],
    ])('%s: %s', async (channelType) => {
      useMessageInputContextMocked.mockReturnValue({
        imageUploads: [],
        fileUploads: [],
      } as Partial<StreamChatExpoModule.MessageInputContextValue>);

      renderLocationPicker(channelType);

      expect(await screen.findByTestId('bottomsheet-location-picker-trigger')).toBeOnTheScreen();
    });
  });
});
