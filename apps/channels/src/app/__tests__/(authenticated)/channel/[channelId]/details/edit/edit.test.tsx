import { patchApiProjectsProjectIdGroupsGroupIdMockHandler } from '@shape-construction/api/handlers-factories/projects/groups';
import { Toast } from '@shape-construction/arch-ui-native';
import { Stack } from 'expo-router';
import { EditChannelPage } from 'src/app/(authenticated)/channel/[channelId]/details/edit';
import { channelContextFactory } from 'src/get-stream/test/factories/chat-context';
import { server } from 'src/tests/mock-server';
import { renderRouter, screen } from 'src/tests/test-utils';

const mockedsShowActionSheetWithOptions = jest.fn();
jest.mock('@expo/react-native-action-sheet', () => ({
  useActionSheet: jest.fn(() => ({
    showActionSheetWithOptions: mockedsShowActionSheetWithOptions,
  })),
}));

describe('<EditChannelPage />', () => {
  it('renders Done button disabled by default', async () => {
    renderRouter(
      {
        '/(authenticated)/channel/[channelId]/details/_layout': () => <Stack />,
        '/(authenticated)/channel/[channelId]/details/edit': EditChannelPage,
      },
      { initialUrl: '/(authenticated)/channel/group:channel-1/details/edit' }
    );

    expect(await screen.findByRole('button', { name: 'channel.details.edit.actions.save' })).toBeDisabled();
  });

  it('populates form with channel data', async () => {
    renderRouter(
      {
        '/(authenticated)/channel/[channelId]/details/_layout': () => <Stack />,
        '/(authenticated)/channel/[channelId]/details/edit': EditChannelPage,
      },
      { initialUrl: '/(authenticated)/channel/group:channel-1/details/edit' },
      {
        stream: {
          channel: channelContextFactory({
            id: 'channel-1',
            type: 'group',
            data: {
              shape_project_id: 'project-1',
              shape_group_id: 'group-1',
              name: 'Group 01',
              image: 'image-url',
            },
          }),
        },
      }
    );

    expect(screen.getByPlaceholderText('channel.details.edit.namePlaceholder')).toHaveDisplayValue('Group 01');
    expect(screen.getByRole('image').props.source.uri).toBe('image-url');
    expect(await screen.findByRole('button', { name: 'channel.details.edit.editPhoto' })).toBeOnTheScreen();
  });

  describe('when user clicks on add photo', () => {
    it('opens action sheet with options', async () => {
      const { user } = renderRouter(
        {
          '/(authenticated)/channel/[channelId]/details/_layout': () => <Stack />,
          '/(authenticated)/channel/[channelId]/details/edit': EditChannelPage,
        },
        { initialUrl: '/(authenticated)/channel/group:channel-1/details/edit' },
        {
          stream: {
            channel: channelContextFactory({
              id: 'channel-1',
              type: 'group',
              data: {
                shape_project_id: 'project-1',
                shape_group_id: 'group-1',
                name: 'Group 01',
                image: undefined,
              },
            }),
          },
        }
      );

      await user.press(await screen.findByRole('button', { name: 'channel.details.edit.addPhoto' }));

      expect(mockedsShowActionSheetWithOptions).toHaveBeenCalledWith(
        {
          cancelButtonIndex: 2,
          containerStyle: { paddingBottom: 0 },
          destructiveButtonIndex: [2],
          options: [
            'channel.details.edit.actions.takePhoto',
            'channel.details.edit.actions.choosePhoto',
            'actions.cancel',
          ],
        },
        expect.any(Function)
      );
    });
  });

  describe('when user clicks on edit photo', () => {
    it('opens action sheet with options', async () => {
      const { user } = renderRouter(
        {
          '/(authenticated)/channel/[channelId]/details/_layout': () => <Stack />,
          '/(authenticated)/channel/[channelId]/details/edit': EditChannelPage,
        },
        { initialUrl: '/(authenticated)/channel/group:channel-1/details/edit' },
        {
          stream: {
            channel: channelContextFactory({
              id: 'channel-1',
              type: 'group',
              data: {
                shape_project_id: 'project-1',
                shape_group_id: 'group-1',
                name: 'Group 01',
                image: 'image-url',
              },
            }),
          },
        }
      );

      await user.press(await screen.findByRole('button', { name: 'channel.details.edit.editPhoto' }));

      expect(mockedsShowActionSheetWithOptions).toHaveBeenCalledWith(
        {
          cancelButtonIndex: 3,
          containerStyle: { paddingBottom: 0 },
          destructiveButtonIndex: [3],
          options: [
            'channel.details.edit.actions.takePhoto',
            'channel.details.edit.actions.choosePhoto',
            'channel.details.edit.actions.removePhoto',
            'actions.cancel',
          ],
        },
        expect.any(Function)
      );
    });
  });

  describe('when user fills the form', () => {
    it('enables save button', async () => {
      const { user } = renderRouter(
        {
          '/(authenticated)/channel/[channelId]/details/_layout': () => <Stack />,
          '/(authenticated)/channel/[channelId]/details/edit': EditChannelPage,
        },
        { initialUrl: '/(authenticated)/channel/group:channel-1/details/edit' },
        {
          stream: {
            channel: channelContextFactory({
              id: 'channel-1',
              type: 'group',
              data: {
                shape_project_id: 'project-1',
                shape_group_id: 'group-1',
                name: 'Group 01',
                image: 'image-url',
              },
            }),
          },
        }
      );

      await user.type(screen.getByPlaceholderText('channel.details.edit.namePlaceholder'), 'New Group Name');

      expect(await screen.findByRole('button', { name: 'channel.details.edit.actions.save' })).toBeEnabled();
    });

    describe('when user clicks save', () => {
      describe('when mutation is successful', () => {
        it('shows success toast and return back to the details page', async () => {
          const spyOnToast = jest.spyOn(Toast, 'show');
          server.use(patchApiProjectsProjectIdGroupsGroupIdMockHandler());
          const { user } = renderRouter(
            {
              '/(authenticated)/channel/[channelId]/details/_layout': () => <Stack />,
              '/(authenticated)/channel/[channelId]/details/edit': EditChannelPage,
            },
            { initialUrl: '/(authenticated)/channel/group:channel-1/details/edit' },
            {
              stream: {
                channel: channelContextFactory({ id: 'channel-1', type: 'group' }),
              },
            }
          );

          await user.type(screen.getByPlaceholderText('channel.details.edit.namePlaceholder'), 'New Group Name');
          await user.press(
            await screen.findByRole('button', { name: 'channel.details.edit.actions.save', disabled: false })
          );

          expect(spyOnToast).toHaveBeenCalledWith('channel.details.edit.success');
          expect(screen).toHavePathname('/channel/group:channel-1/details/edit');
        });
      });

      describe('when mutation fails', () => {
        it('shows error toast and does not redirect', async () => {
          const spyOnToast = jest.spyOn(Toast, 'show');
          server.use(patchApiProjectsProjectIdGroupsGroupIdMockHandler(undefined, { status: 500 }));
          const { user } = renderRouter(
            {
              '/(authenticated)/channel/[channelId]/details/_layout': () => <Stack />,
              '/(authenticated)/channel/[channelId]/details/edit': EditChannelPage,
            },
            { initialUrl: '/(authenticated)/channel/group:channel-1/details/edit' },
            {
              stream: {
                channel: channelContextFactory({ id: 'channel-1', type: 'group' }),
              },
            }
          );

          await user.type(screen.getByPlaceholderText('channel.details.edit.namePlaceholder'), 'New Group Name');
          await user.press(
            await screen.findByRole('button', { name: 'channel.details.edit.actions.save', disabled: false })
          );

          expect(spyOnToast).toHaveBeenCalledWith('channel.details.edit.error');
          expect(screen).toHavePathname('/channel/group:channel-1/details/edit');
        });
      });
    });
  });
});
