import { userFactory } from '@shape-construction/api/factories/users';
import { useSession } from 'src/authentication/SessionProvider';
import { renderRouter, screen, waitFor } from 'src/tests/test-utils';
import { SettingsScreen } from '../../../(authenticated)/settings/index';

jest.mock('stream-chat-expo', () => ({
  ...jest.requireActual('stream-chat-expo'),
  SqliteClient: { resetDB: jest.fn() },
}));

jest.mock('src/authentication/SessionProvider', () => ({
  ...jest.requireActual('src/authentication/SessionProvider'),
  useSession: jest.fn(),
}));

const sessionUser = userFactory({ name: '<PERSON>' });

describe('SettingsScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.mocked(useSession).mockReturnValue({
      user: sessionUser,
      signOut: jest.fn(),
    });
  });

  it('renders the settings page', async () => {
    renderRouter(
      {
        '/(authenticated)/settings': SettingsScreen,
      },
      { initialUrl: '/(authenticated)/settings' }
    );

    expect(await screen.findByRole('link', { name: 'Richard Branson' })).toBeOnTheScreen();
    expect(screen.getByRole('link', { name: 'settings.helpcenter' })).toBeOnTheScreen();
    expect(screen.getByRole('button', { name: 'settings.logout' })).toBeOnTheScreen();
  });

  describe('when clicking on the user avatar', () => {
    it('navigates to the profile page', async () => {
      const { user } = renderRouter(
        {
          '/(authenticated)/settings/': SettingsScreen,
          '/(authenticated)/settings/profile/': () => null,
        },
        { initialUrl: '/(authenticated)/settings' }
      );

      await user.press(await screen.findByRole('link', { name: 'Richard Branson' }));

      await waitFor(() => expect(screen).toHavePathname('/settings/profile/'));
    });
  });

  describe('when clicking on helpcenter', () => {
    it('navigates to the helpcenter page', async () => {
      const { user } = renderRouter(
        {
          '/(authenticated)/settings/': SettingsScreen,
          '/(authenticated)/settings/help-center/': () => null,
        },
        { initialUrl: '/(authenticated)/settings' }
      );

      await user.press(screen.getByRole('link', { name: 'settings.helpcenter' }));

      await waitFor(() => expect(screen).toHavePathname('/settings/help-center/'));
    });
  });

  describe('when clicking on Log out', () => {
    it('logs out the user', async () => {
      const mockLogout = jest.fn();
      (useSession as jest.Mock).mockReturnValue({
        user: userFactory({ name: 'Richard Branson' }),
        signOut: mockLogout,
      });
      const { user } = renderRouter(
        {
          '/(authenticated)/settings/': SettingsScreen,
        },
        { initialUrl: '/(authenticated)/settings' }
      );

      await user.press(screen.getByRole('button', { name: 'settings.logout' }));
      await user.press(screen.getByRole('button', { name: 'actions.yes' }));

      expect(mockLogout).toHaveBeenCalled();
    });
  });
});
