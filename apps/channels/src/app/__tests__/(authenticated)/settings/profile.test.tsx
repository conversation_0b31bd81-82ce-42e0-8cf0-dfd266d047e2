import { userFactory } from '@shape-construction/api/factories/users';
import { patchApiUsersMeMockHandler } from '@shape-construction/api/handlers-factories/users';
import type { PatchApiUsersMeMutationRequestSchema, UserSchema } from '@shape-construction/api/src/types';
import { Stack } from 'expo-router';
import { ProfileScreen } from 'src/app/(authenticated)/settings/profile';
import { server } from 'src/tests/mock-server';
import { renderRouter, screen, waitFor } from 'src/tests/test-utils';

describe('Profile screen index', () => {
  it('renders correctly', async () => {
    renderRouter(
      {
        '/settings/profile/index': ProfileScreen,
      },
      {
        initialUrl: '/settings/profile',
      },
      {
        user: userFactory({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          avatarUrl: null,
        }),
      }
    );

    expect(screen.getByText('settings.profile.edit')).toBeOnTheScreen();
    expect(screen.getByText('settings.profile.info')).toBeOnTheScreen();
    expect(screen.getByText('settings.profile.firstName')).toBeOnTheScreen();
    expect(screen.getByText('settings.profile.lastName')).toBeOnTheScreen();
    expect(screen.getByText('settings.profile.email')).toBeOnTheScreen();
    await waitFor(() =>
      expect(screen.getByPlaceholderText('settings.profile.placeholder.firstName')).toHaveDisplayValue('John')
    );
    await waitFor(() =>
      expect(screen.getByPlaceholderText('settings.profile.placeholder.lastName')).toHaveDisplayValue('Doe')
    );
    await waitFor(() =>
      expect(screen.getByPlaceholderText('settings.profile.placeholder.email')).toHaveDisplayValue(
        '<EMAIL>'
      )
    );
  });

  it('saves profile when there are changes', async () => {
    let signupPathBody: PatchApiUsersMeMutationRequestSchema | undefined;
    server.use(
      patchApiUsersMeMockHandler(async ({ request }) => {
        const body = await request.json();
        signupPathBody = body;
        return userFactory(body.user as UserSchema);
      })
    );
    const { user } = renderRouter(
      {
        '/settings/profile/_layout': () => <Stack />,
        '/settings/profile/index': ProfileScreen,
      },
      {
        initialUrl: '/settings/profile',
      },
      {
        user: userFactory({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          avatarUrl: null,
        }),
      }
    );
    const firstNameInput = screen.getByPlaceholderText('settings.profile.placeholder.firstName');
    // wait for data to load
    await waitFor(() => expect(firstNameInput).toHaveDisplayValue('John'));

    user.clear(firstNameInput);
    user.type(firstNameInput, 'Jane');
    user.press(await screen.findByText('actions.save'));

    await waitFor(() =>
      expect(signupPathBody).toEqual({
        user: {
          email: '<EMAIL>',
          first_name: 'Jane',
          last_name: 'Doe',
        },
      })
    );
  });
});
