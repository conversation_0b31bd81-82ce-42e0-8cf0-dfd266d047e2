import { userFactory } from '@shape-construction/api/factories/users';
import { getApiUsersMeMockHandler } from '@shape-construction/api/handlers-factories/users';
import { AuthenticatedLayout } from 'src/app/(authenticated)/_layout';
import { registerBackgroundTokenRefresh } from 'src/background_task/refresh_token_task';
import { server } from 'src/tests/mock-server';
import { render, waitFor } from 'src/tests/test-utils';

jest.mock('src/get-stream/ChatClientProvider', () => ({
  ChatClientProvider: jest.fn(),
}));

jest.mock('src/background_task/refresh_token_task', () => ({
  registerBackgroundTokenRefresh: jest.fn(),
}));

// Mock the authentication tokens to prevent getToken errors
jest.mock('src/authentication/tokens', () => ({
  getToken: () => 'mock-token',
}));

jest.mock('expo-router', () => ({
  ...jest.requireActual('expo-router'),
  usePathname: jest.fn(),
}));

describe('<_Layout />', () => {
  it('register background fetch at mount', async () => {
    server.use(getApiUsersMeMockHandler(() => userFactory()));

    render(<AuthenticatedLayout />);

    await waitFor(() => expect(registerBackgroundTokenRefresh).toHaveBeenCalled());
  });
});
