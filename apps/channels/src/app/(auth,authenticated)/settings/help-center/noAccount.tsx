import React from 'react';
import { List } from '@shape-construction/arch-ui-native';
import { ChevronRightIcon, UserCircleIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import * as Linking from 'expo-linking';
import { Link, Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { email } from 'src/components/HelpCenter/components/Footer/HelpCenterFooter';

export const NoAccountPage = () => {
  const { t } = useTranslation();

  const openSupportEmail = () => {
    Linking.openURL(`mailto:${email}`);
  };

  return (
    <SafeAreaView edges={['bottom']} className="flex-1">
      <Stack.Screen
        options={{
          headerShown: true,
          title: t('helpcenter.noAccount.headerTitle'),
        }}
      />
      <ScrollView className="flex-1 bg-neutral-subtlest flex flex-col">
        {/* Header info */}
        <View className="py-8 px-6 flex flex-col justify-center items-center gap-2">
          <UserCircleIcon className="h-12 w-12 text-brand" />
          <Text className="text-xl leading-7 font-semibold">{t('helpcenter.noAccount.title')}</Text>
          <Text className="text-center text-sm leading-5 font-normal text-gray-500">
            {t('helpcenter.noAccount.description.0')}
          </Text>
          <Text className="text-center text-sm leading-5 font-normal text-gray-500">
            {t('helpcenter.noAccount.description.1')}
          </Text>
        </View>

        {/* Actions list */}
        <View className="flex-1 bg-white flex flex-col gap-8 mb-8">
          <List.Root>
            <List.Divider />
            <List.Item asChild className="py-4">
              <Link
                href={{
                  pathname: '/settings/help-center',
                }}
                asChild
              >
                <TouchableOpacity>
                  <List.Content className="flex flex-col gap-1">
                    <List.Title className="text-base leading-6 font-medium text-gray-900">
                      {t('helpcenter.noAccount.visitHelpCenter')}
                    </List.Title>
                  </List.Content>
                  <ChevronRightIcon className="text-gray-500 w-5 h-5" />
                </TouchableOpacity>
              </Link>
            </List.Item>
            <List.Divider />
            <List.Item asChild className="py-4">
              <TouchableOpacity onPress={openSupportEmail}>
                <List.Content className="flex flex-col gap-1">
                  <List.Title className="text-base leading-6 font-medium text-gray-900">
                    {t('helpcenter.noAccount.contact')}
                  </List.Title>
                  <List.SupportingText className="text-sm leading-5 font-normal text-gray-500">
                    {email}
                  </List.SupportingText>
                </List.Content>
                <ChevronRightIcon className="text-gray-500 w-5 h-5" />
              </TouchableOpacity>
            </List.Item>
            <List.Divider />
          </List.Root>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default NoAccountPage;
