import React from 'react';
import { Divider } from '@shape-construction/arch-ui-native/src/primitives/List';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ScrollView, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { HelpCenterFooter } from 'src/components/HelpCenter/components/Footer/HelpCenterFooter';

export const HelpCenterArticlePage = () => {
  const { t } = useTranslation();
  const { articleId } = useLocalSearchParams<{ articleId?: string }>();
  const articles = t('helpcenter.articles', { returnObjects: true });
  const article = articles.find(({ id }) => id === articleId);

  return (
    <SafeAreaView edges={['bottom']} className="flex-1">
      <Stack.Screen
        options={{
          title: t('helpcenter.article.title'),
        }}
      />
      <ScrollView className="flex-1 bg-neutral-subtlest flex flex-col gap-4 p-4">
        <View className="flex flex-col gap-4">
          <Text className="text-2xl leading-8 font-semibold text-neutral">{article?.title}</Text>
          <View className="flex flex-col gap-4">
            {article?.supportingText.map((text) => (
              <Text key={text} className="text-base leading-6 font-normal text-neutral">
                {text}
              </Text>
            ))}
          </View>
        </View>
        <View className="flex flex-col gap-8">
          <Divider className="mt-8 max-w-md mx-auto w-full border-[0.5px] border-neutral" />
          <HelpCenterFooter />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HelpCenterArticlePage;
