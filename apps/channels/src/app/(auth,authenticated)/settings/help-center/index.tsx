import React, { Fragment } from 'react';
import { List } from '@shape-construction/arch-ui-native';
import { ChevronRightIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Link } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { HelpCenterFooter } from 'src/components/HelpCenter/components/Footer/HelpCenterFooter';
import { ShapeLogo } from 'src/components/ShapeLogo';

export const HelpCenterPage = () => {
  const { t } = useTranslation();
  const articles = t('helpcenter.articles', { returnObjects: true });

  return (
    <SafeAreaView edges={['bottom']} className="flex-1">
      <ScrollView className="flex-1 bg-neutral-subtlest flex flex-col">
        {/* Header info */}
        <View className="py-8 flex flex-col justify-center items-center gap-2">
          <ShapeLogo className="h-10 w-10" />
          <Text className="text-xl leading-7 font-semibold">{t('helpcenter.articlesList.title')}</Text>
        </View>

        {/* Actions list */}
        <View className="flex-1 bg-white flex flex-col gap-8 mb-8">
          <List.Root>
            <List.Divider />
            {articles.map(({ id, title }) => (
              <Fragment key={title}>
                <List.Item asChild className="py-4">
                  <Link
                    href={{
                      pathname: '/settings/help-center/articles/[articleId]',
                      params: { articleId: id },
                    }}
                    asChild
                  >
                    <TouchableOpacity>
                      <List.Content className="flex flex-col gap-1">
                        <List.Title className="text-base leading-6 font-medium text-gray-900">{title}</List.Title>
                      </List.Content>
                      <ChevronRightIcon className="text-gray-500 w-5 h-5" />
                    </TouchableOpacity>
                  </Link>
                </List.Item>
                <List.Divider />
              </Fragment>
            ))}
          </List.Root>
        </View>

        {/* Footer */}
        <HelpCenterFooter />
      </ScrollView>
    </SafeAreaView>
  );
};

export default HelpCenterPage;
