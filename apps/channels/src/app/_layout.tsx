import 'src/global.css';
import 'src/localization/i18n';

import { useEffect } from 'react';
import { PortalProvider } from '@gorhom/portal';
import * as Sentry from '@sentry/react-native';
import { theme } from '@shape-construction/arch-ui-native/src/theme';
import { ThemeProvider } from '@shape-construction/arch-ui-native/src/theme/Theme';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import Constants, { ExecutionEnvironment } from 'expo-constants';
import { Slot, useNavigationContainerRef } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import * as WebBrowser from 'expo-web-browser';
import { KeyboardAvoidingView, Platform } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { RootSiblingParent } from 'react-native-root-siblings';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { registerForegroundEvent, registerNotificationsInteraction } from 'src/get-stream/notifications';
import { asyncStoragePersister, queryClient } from 'src/react-query/query-client';
import { environment } from '../config/environment';

WebBrowser.maybeCompleteAuthSession();

const navigationIntegration = Sentry.reactNavigationIntegration({
  enableTimeToInitialDisplay: Constants.executionEnvironment === ExecutionEnvironment.StoreClient,
});

Sentry.init({
  dsn: environment.SENTRY_DSN,
  environment: environment.APP_ENV,
  integrations: [navigationIntegration],
  enableNativeFramesTracking: Constants.executionEnvironment === ExecutionEnvironment.StoreClient,
  release: environment.VERSION,
});

export const RootLayout = () => {
  const ref = useNavigationContainerRef();

  useEffect(() => {
    registerNotificationsInteraction();
    return registerForegroundEvent();
  }, []);

  useEffect(() => {
    if (!environment.SENTRY_DSN) return;
    if (!ref) return;

    navigationIntegration.registerNavigationContainer(ref);
  }, [ref]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <StatusBar style="dark" backgroundColor={theme.colors.white} />
      <PersistQueryClientProvider client={queryClient} persistOptions={{ persister: asyncStoragePersister }}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          className="flex-1 bg-white justify-center items-stretch"
        >
          <ThemeProvider theme={theme}>
            <PortalProvider>
              <RootSiblingParent>
                <SafeAreaProvider>
                  <Slot />
                </SafeAreaProvider>
              </RootSiblingParent>
            </PortalProvider>
          </ThemeProvider>
        </KeyboardAvoidingView>
      </PersistQueryClientProvider>
    </GestureHandlerRootView>
  );
};

export default Sentry.wrap(RootLayout);
