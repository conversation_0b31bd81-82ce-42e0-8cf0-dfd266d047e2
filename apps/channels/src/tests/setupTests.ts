import '@testing-library/react-native/extend-expect';
import 'src/localization/i18n';
import mockSafeAreaContext from 'react-native-safe-area-context/jest/mock';
import { mockClient } from 'src/get-stream/test/mock';
import { server } from './mock-server';

jest.mock('src/get-stream/sdkClient', () => ({ chatClient: mockClient() }));

jest.mock('src/localization/i18n');

jest.mock('react-native/Libraries/EventEmitter/NativeEventEmitter');

jest.mock('react-native-safe-area-context', () => mockSafeAreaContext);
jest.mock('expo-task-manager');
jest.mock('expo-background-fetch');
jest.mock('expo-secure-store');
jest.mock('expo-constants');
jest.mock('@sentry/react-native');
jest.mock(
  'expo-linking',
  () =>
    ({
      ...jest.requireActual('expo-linking'),
      createURL: jest.fn(),
    }) satisfies typeof import('expo-linking')
);
jest.mock(
  'expo-media-library',
  () =>
    ({
      ...jest.requireActual('expo-media-library'),
      requestPermissionsAsync: jest.fn(),
      saveToLibraryAsync: jest.fn(),
    }) satisfies typeof import('expo-media-library')
);

jest.mock(
  'expo-clipboard',
  () =>
    ({
      ...jest.requireActual('expo-clipboard'),
      setStringAsync: jest.fn(),
    }) satisfies typeof import('expo-clipboard')
);

jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

beforeAll(() => {
  server.listen();
});

afterEach(() => {
  server.resetHandlers();
});

afterAll(() => {
  server.close();
});
