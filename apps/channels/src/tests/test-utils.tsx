import React from 'react';
import { PortalProvider } from '@gorhom/portal';
import { initialiseAxiosClient } from '@shape-construction/api/client';
import { userFactory as shapeUserFactory } from '@shape-construction/api/factories/users';
import type { UserSchema } from '@shape-construction/api/src/types';
import { QueryClientProvider } from '@tanstack/react-query';
import {
  type RenderHookOptions,
  type RenderOptions,
  render as rtlRender,
  renderHook as rtlRenderHook,
  userEvent,
} from '@testing-library/react-native';
import type { FileStub } from 'expo-router/build/testing-library/context-stubs';
import { renderRouter as expoRenderRouter } from 'expo-router/testing-library';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { axiosInstance } from 'src/axios/custom-instance';
import { channelContextFactory, chatContextValueFactory } from 'src/get-stream/test/factories/chat-context';
import { userFactory } from 'src/get-stream/test/factories/user';
import { mockClient } from 'src/get-stream/test/mock';
import type { ChannelContextValue } from 'stream-chat-expo';
import { ChannelProvider, ChatProvider, ThemeProvider } from 'stream-chat-expo';
import { SessionProvider } from '../authentication/SessionProvider';
import { buildQueryClient } from '../react-query/query-client';

type RenderContext = {
  user?: UserSchema;
  stream?: {
    chat?: ReturnType<typeof chatContextValueFactory>;
    channel?: ReturnType<typeof channelContextFactory>;
    client?: ReturnType<typeof mockClient>;
  };
};

initialiseAxiosClient(axiosInstance);

const buildWrapper = ({ user = shapeUserFactory(), stream }: RenderContext, Wrapper?: React.ComponentType<any>) => {
  const Providers: RenderOptions['wrapper'] = (props) => {
    const queryClient = buildQueryClient();

    // stream sdk
    const streamClient = stream?.client || mockClient({ user: userFactory({ name: user.name }) });
    const streamChannel = stream?.channel || channelContextFactory({ client: streamClient });
    const streamChat = stream?.chat || chatContextValueFactory({ client: streamClient, channel: streamChannel });

    const children = Wrapper ? <Wrapper {...props} /> : props.children;

    return (
      <QueryClientProvider client={queryClient}>
        <SessionProvider user={user}>
          <PortalProvider>
            <ChatProvider value={streamChat}>
              <ChannelProvider value={{ channel: streamChannel } as ChannelContextValue}>
                <ThemeProvider>
                  <SafeAreaProvider>{children}</SafeAreaProvider>
                </ThemeProvider>
              </ChannelProvider>
            </ChatProvider>
          </PortalProvider>
        </SessionProvider>
      </QueryClientProvider>
    );
  };

  return Providers;
};

const render = <T,>(ui: React.ReactElement<T>, options: RenderOptions = {}, context: RenderContext = {}) => {
  return {
    ...rtlRender(ui, { ...options, wrapper: buildWrapper(context, options.wrapper) }),
    user: userEvent.setup(),
  };
};

const renderRouter = (
  routeConfig: string | Record<string, FileStub> | { appDir: string; overrides: Record<string, FileStub> },
  options: RenderOptions & { initialUrl?: string } = {},
  context: RenderContext = {}
) => {
  return {
    ...expoRenderRouter(routeConfig, {
      ...options,
      wrapper: buildWrapper(context, options.wrapper),
    }),
    user: userEvent.setup(),
  };
};

const renderHook = <TProps, TResult>(
  hook: (props: TProps) => TResult,
  options: RenderHookOptions<TProps> = {},
  context: RenderContext = {}
) => rtlRenderHook(hook, { ...options, wrapper: buildWrapper(context, options.wrapper) });

export { act, fireEvent, screen, userEvent, waitFor } from '@testing-library/react-native';
export { render, renderHook, renderRouter };
