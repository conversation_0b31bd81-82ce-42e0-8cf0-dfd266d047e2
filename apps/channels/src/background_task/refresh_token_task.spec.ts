import type { AxiosError } from 'axios';
import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import { refreshAuthToken } from 'src/authentication/authorization';
import { clearToken, getRefreshToken } from 'src/authentication/tokens';
import { BACKGROUND_TOKEN_REFRESH_TASK, registerBackgroundTokenRefresh, taskHandler } from './refresh_token_task';

jest.mock('src/authentication/tokens');
jest.mock('src/authentication/authorization');

const mockedTaskManagerIsTaskRegisteredAsync = jest.mocked(TaskManager.isTaskRegisteredAsync);
const mockedGetRefreshToken = jest.mocked(getRefreshToken);
const mockedRefreshAuthToken = jest.mocked(refreshAuthToken);
const mockedClearToken = jest.mocked(clearToken);

describe('refresh_token_task', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('registerBackgroundTokenRefresh', () => {
    describe('when the task is not registered', () => {
      it('register the task without calling unregister', async () => {
        mockedTaskManagerIsTaskRegisteredAsync.mockResolvedValue(false);

        await registerBackgroundTokenRefresh();

        expect(BackgroundFetch.unregisterTaskAsync).not.toHaveBeenCalled();
        expect(BackgroundFetch.registerTaskAsync).toHaveBeenCalled();
      });
    });

    describe('when the task is already registered', () => {
      it('forces unregister and register the task', async () => {
        mockedTaskManagerIsTaskRegisteredAsync.mockResolvedValue(true);

        await registerBackgroundTokenRefresh();

        expect(BackgroundFetch.unregisterTaskAsync).toHaveBeenCalled();
        expect(BackgroundFetch.registerTaskAsync).toHaveBeenCalled();
      });
    });

    it('register the task when it is not registered', async () => {
      mockedTaskManagerIsTaskRegisteredAsync.mockResolvedValue(false);

      await registerBackgroundTokenRefresh();

      expect(mockedTaskManagerIsTaskRegisteredAsync).toHaveBeenCalledWith(BACKGROUND_TOKEN_REFRESH_TASK);
      expect(BackgroundFetch.registerTaskAsync).toHaveBeenCalled();
    });
  });

  describe('taskHandler', () => {
    it('returns noData when there is no authorization', async () => {
      mockedGetRefreshToken.mockReturnValue(null);

      const backgroundFetchResult = await taskHandler();

      expect(mockedGetRefreshToken).toHaveBeenCalled();
      expect(backgroundFetchResult).toBe(BackgroundFetch.BackgroundFetchResult.NoData);
    });

    it('returns NewData when the token is refreshed successfully', async () => {
      mockedGetRefreshToken.mockReturnValue('mock-token');

      const backgroundFetchResult = await taskHandler();

      expect(mockedGetRefreshToken).toHaveBeenCalled();
      expect(mockedRefreshAuthToken).toHaveBeenCalled();
      expect(backgroundFetchResult).toBe(BackgroundFetch.BackgroundFetchResult.NewData);
    });

    describe('when the token refresh fails (400)', () => {
      it('returns false', async () => {
        const error = {
          isAxiosError: true,
          config: { method: 'get' },
          response: { status: 400, config: { url: '/api/some/endpoint' } },
        } as AxiosError;
        mockedGetRefreshToken.mockReturnValue('mock-token');
        mockedRefreshAuthToken.mockRejectedValue(error);

        const backgroundFetchResult = await taskHandler();

        expect(mockedGetRefreshToken).toHaveBeenCalled();
        expect(mockedRefreshAuthToken).toHaveBeenCalled();
        expect(mockedClearToken).not.toHaveBeenCalled();
        expect(backgroundFetchResult).toBe(BackgroundFetch.BackgroundFetchResult.Failed);
      });
    });

    describe('when the token refresh fails (401)', () => {
      it('returns false', async () => {
        const error = {
          isAxiosError: true,
          config: { method: 'get' },
          response: { status: 401, config: { url: '/api/some/endpoint' } },
        } as AxiosError;
        mockedGetRefreshToken.mockReturnValue('mock-token');
        mockedRefreshAuthToken.mockRejectedValue(error);

        const backgroundFetchResult = await taskHandler();

        expect(mockedGetRefreshToken).toHaveBeenCalled();
        expect(mockedRefreshAuthToken).toHaveBeenCalled();
        expect(mockedClearToken).toHaveBeenCalled();
        expect(backgroundFetchResult).toBe(BackgroundFetch.BackgroundFetchResult.Failed);
      });
    });
  });
});
