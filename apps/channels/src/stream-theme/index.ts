import type { Colors } from '@shape-construction/arch-ui-native/src/theme/colors-primitives';
import type { TextStyle } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { type DeepPartial, type Theme, useViewport } from 'stream-chat-expo';
import resolveConfig from 'tailwindcss/resolveConfig';
import type { DefaultTheme } from 'tailwindcss/types/generated/default-theme';
import tailwindConfig from '../../tailwind.config';

function remToPixels(rem: string): number {
  const baseFontSize = 16;
  const remValue = Number.parseFloat(rem);
  return remValue * baseFontSize;
}

const tw = resolveConfig(tailwindConfig);
type TailwindTheme = (typeof tw)['theme'];

const { theme } = tw as unknown as {
  theme: Omit<TailwindTheme, 'colors'> & {
    colors: Colors;
    fontWeight: Record<keyof DefaultTheme['fontWeight'], TextStyle['fontWeight']>;
  };
};

export const useStreamTheme = (): DeepPartial<Theme> => {
  const { vw } = useViewport();
  const { top: topPadding, bottom: bottomPadding } = useSafeAreaInsets();
  return {
    imageGallery: {
      footer: {
        container: {
          paddingBottom: bottomPadding,
        },
      },
      header: {
        container: {
          paddingTop: topPadding,
        },
      },
    },
    colors: {
      accent_blue: theme.colors['indigo-500'],
    },
    loadingIndicator: {
      container: {
        backgroundColor: 'rgba(239, 234, 227, 1)',
      },
    },
    emptyStateIndicator: {
      channelContainer: {
        backgroundColor: 'rgba(239, 234, 227, 1)',
      },
    },
    messageList: {
      container: {
        backgroundColor: 'rgba(239, 234, 227, 1)',
      },
    },
    channelPreview: {
      container: {
        paddingHorizontal: 23,
        paddingVertical: 15,
      },
      date: {
        // text-sm/leading-5/font-normal color/text/neutral/subtle
        fontSize: remToPixels(theme.fontSize.sm[0]),
        fontWeight: theme.fontWeight.normal,
        lineHeight: remToPixels(theme.lineHeight[5]),
        color: 'rgba(107, 114, 128, 1)',
      },
      title: {
        // text-sm/leading-5/font-medium color/text/neutral/bold
        fontSize: remToPixels(theme.fontSize.sm[0]),
        fontWeight: theme.fontWeight.medium,
        lineHeight: remToPixels(theme.lineHeight[5]),
        color: 'rgba(31, 41, 55, 1)',
      },
      message: {
        // text-sm/leading-5/font-normal rgba(107, 114, 128, 1)
        fontSize: remToPixels(theme.fontSize.sm[0]),
        fontWeight: theme.fontWeight.normal,
        lineHeight: remToPixels(theme.lineHeight[5]),
        color: 'rgba(107, 114, 128, 1)',
      },
    },
    messageSimple: {
      unreadUnderlayColor: 'transparent',
      fileAttachmentGroup: {
        container: {
          backgroundColor: 'rgba(255,255,255, 0.2)',
        },
      },
      content: {
        containerInner: {
          backgroundColor: theme.colors.white,
          alignItems: 'stretch',
        },
        replyContainer: {
          minWidth: vw(100) - 128,
          maxWidth: vw(100) - 128,
          paddingTop: 8,
          paddingBottom: 8,
        },
        textContainer: {
          paddingHorizontal: 12,
          maxWidth: vw(100) - 128,
        },
        markdown: {
          text: {
            // text-base/leading-6/font-normal text-black
            color: theme.colors.black,
            fontSize: remToPixels(theme.fontSize.base[0]),
            fontWeight: theme.fontWeight.normal,
            lineHeight: remToPixels(theme.lineHeight[6]),
          },
          mentions: {
            // text-base/leading-6/font-normal text-black
            color: theme.colors['indigo-700'],
            fontSize: remToPixels(theme.fontSize.base[0]),
            fontWeight: theme.fontWeight.normal,
            lineHeight: remToPixels(theme.lineHeight[6]),
          },
        },
      },
      gallery: {
        minWidth: vw(100) - 136,
        maxWidth: vw(100) - 136,
        gridWidth: vw(100) - 136,
        galleryItemColumn: {
          flex: 1,
          justifyContent: 'space-around',
          height: '100%',
        },
        galleryContainer: {
          marginLeft: 0,
          marginRight: 0,
          marginTop: 4,
          borderBottomRightRadius: 16,
          borderBottomLeftRadius: 16,
        },
        imageContainer: {
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
        },
      },
      status: {
        checkAllIcon: {
          color: theme.colors['indigo-700'],
          height: 16,
          width: 18,
          viewBox: '0 2.5 25 10',
        },
        checkIcon: {
          height: 16,
          width: 18,
          viewBox: '0 2.5 25 10',
        },
        statusContainer: {
          marginTop: 0.5,
          marginLeft: 0.5,
        },
      },
    },
    messageInput: {
      container: {
        padding: 0,
      },
      replyContainer: {
        paddingBottom: 0,
      },
      composerContainer: {
        padding: 10,
      },
    },
  };
};

export const useMyMessageStreamTheme = (): DeepPartial<Theme> => {
  const { vw } = useViewport();

  return {
    messageSimple: {
      content: {
        replyContainer: {
          minWidth: vw(100) - 84,
          maxWidth: vw(100) - 84,
        },
        containerInner: {
          backgroundColor: theme.colors['indigo-200'],
          borderColor: theme.colors.transparent,
          maxWidth: '100%',
        },
        markdown: {
          text: {
            color: theme.colors.black,
          },
        },
        textContainer: {
          maxWidth: vw(100) - 80,
        },
      },
      gallery: {
        galleryItemColumn: { flex: 1, justifyContent: 'space-around', height: '100%' },
        gridWidth: vw(100) - 100,
        maxWidth: vw(100) - 100,
        minWidth: vw(100) - 100,
      },
      file: {
        container: {
          backgroundColor: theme.colors.transparent,
          borderColor: theme.colors.transparent,
        },
      },
    },
  };
};
