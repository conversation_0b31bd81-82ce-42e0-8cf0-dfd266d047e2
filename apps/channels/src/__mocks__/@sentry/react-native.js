// Mock @sentry/react-native to prevent timer leaks
export default {
  init: jest.fn(),
  captureException: jest.fn(),
  captureMessage: jest.fn(),
  setTag: jest.fn(),
  setUser: jest.fn(),
  setContext: jest.fn(),
  addBreadcrumb: jest.fn(),
  startTransaction: jest.fn(),
  getCurrentHub: jest.fn(() => ({
    captureException: jest.fn(),
    captureMessage: jest.fn(),
  })),
  withScope: jest.fn((callback) => callback({ setTag: jest.fn(), setContext: jest.fn() })),
  configureScope: jest.fn((callback) => callback({ setTag: jest.fn(), setContext: jest.fn() })),
};
