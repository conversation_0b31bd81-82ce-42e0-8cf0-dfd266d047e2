import { useEffect, useRef } from 'react';
import notifee, { AuthorizationStatus } from '@notifee/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import type { User } from '@react-native-google-signin/google-signin';
import { useGlobalSearchParams } from 'expo-router';
import { Platform } from 'react-native';
import { usePushNotificationPermission } from 'src/components/PushNotificationPermission';
import type { PushProvider } from 'stream-chat';
import { environment } from '../../config/environment';
import { chatClient } from '../sdkClient';
import { displayNotification } from './displayNotification';
import { updateBadgeCounter } from './notificationBadges';

const pushProvider = environment.PUSH_NOTIFICATIONS_PROVIDER as PushProvider;
const pushProviderName = environment.PUSH_NOTIFICATIONS_PROVIDER_NAME;

interface PushNotificationsHook {
  initPushNotification: (streamChatUserId: string) => Promise<void>;
  muteAppNotifications: () => Promise<void>;
  unMuteAppNotifications: (userId: string) => Promise<void>;
}

export const usePushNotifications = (): PushNotificationsHook => {
  const { channelId } = useGlobalSearchParams<{ channelId: string }>();
  const { pushNotificationPermissionStatus } = usePushNotificationPermission();
  const unsubscribeTokenRefreshListenerRef = useRef<(() => void) | undefined>(undefined);
  const unsubscribeOnMessage = useRef<(() => void) | undefined>(undefined);
  const channelIdRef = useRef<string | undefined>(channelId);
  channelIdRef.current = channelId;

  useEffect(() => {
    return () => {
      unsubscribeTokenRefreshListenerRef.current?.();
      unsubscribeOnMessage.current?.();
    };
  }, []);

  const unregisterDevice = async (): Promise<void> => {
    const oldToken = await AsyncStorage.getItem('@current_push_token');

    if (oldToken !== null) {
      await chatClient.removeDevice(oldToken);
    }
  };

  const registerDevice = async (userId: User['user']['id']): Promise<void> => {
    unsubscribeTokenRefreshListenerRef.current?.();

    const token = await messaging().getToken();
    await chatClient.addDevice(token, pushProvider, userId, pushProviderName);
    await AsyncStorage.setItem('@current_push_token', token);
    await AsyncStorage.removeItem('@is_app_notification_muted');

    unsubscribeTokenRefreshListenerRef.current = messaging().onTokenRefresh(async (newToken) => {
      await Promise.all([
        unregisterDevice(),
        chatClient.addDevice(newToken, pushProvider, userId, pushProviderName),
        AsyncStorage.setItem('@current_push_token', newToken),
      ]);
    });
  };

  const registerForegroundNotifications = (): void => {
    //Unregister any previous listener to avoid duplication
    unsubscribeOnMessage.current?.();
    unsubscribeOnMessage.current = messaging().onMessage(async (remoteMessage) => {
      if (channelIdRef.current === remoteMessage.data?.cid) return;

      if (Platform.OS === 'ios') {
        /**
         * iOS notifications count will be present in remoteMessage.notification?.ios?.badge
         * For more information, check push notification "Notification Template" in Stream Dashboard
         */
        const badgeNumber = Number(remoteMessage.notification?.ios?.badge);
        if (Number.isInteger(badgeNumber)) updateBadgeCounter(badgeNumber);
      } else {
        /**
         * Android notifications count will be present in remoteMessage.notification?.android?.count
         * For more information, check push notification "Notification Template" in Stream Dashboard
         */
        const badgeNumber = Number(remoteMessage.notification?.android?.count);
        if (Number.isInteger(badgeNumber)) updateBadgeCounter(badgeNumber);
      }

      displayNotification(remoteMessage);
    });
  };

  const muteAppNotifications = async (): Promise<void> => {
    //deleteToken will not work when push notification is disabled on phones system settings
    if (pushNotificationPermissionStatus === AuthorizationStatus.AUTHORIZED) {
      await messaging().deleteToken();
      await notifee.setBadgeCount(0);
      await AsyncStorage.removeItem('@current_push_token');
    }

    await AsyncStorage.setItem('@is_app_notification_muted', String(1));
  };

  const unMuteAppNotifications = async (userId: string): Promise<void> => {
    //getToken will not work when push notification is disabled on phones system settings
    if (pushNotificationPermissionStatus === AuthorizationStatus.AUTHORIZED) {
      const token = await messaging().getToken();
      await chatClient.addDevice(token, pushProvider, userId, pushProviderName);
    }

    await AsyncStorage.removeItem('@is_app_notification_muted');
  };

  const initPushNotification = async (streamChatUserId: string): Promise<void> => {
    const isAppNotificationMuted = Number(await AsyncStorage.getItem('@is_app_notification_muted'));

    if (isAppNotificationMuted) {
      await muteAppNotifications();
      return;
    }

    await registerDevice(streamChatUserId);
    registerForegroundNotifications();
  };

  return {
    initPushNotification,
    muteAppNotifications,
    unMuteAppNotifications,
  };
};
