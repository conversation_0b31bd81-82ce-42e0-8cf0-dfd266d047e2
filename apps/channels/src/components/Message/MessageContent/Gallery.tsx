import React from 'react';
import { useGetApiProjectsProjectIdLocationsLocationId } from '@shape-construction/api/src/hooks';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { cn } from '@shape-construction/arch-ui-native';
import { MapPinIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Text, View } from 'react-native';
import type { LocalMessage } from 'stream-chat';
import { Gallery as StreamChatGallery, useChannelContext, useMessageContext } from 'stream-chat-expo';

type GalleryProps = {
  message: LocalMessage;
};

export const Gallery: React.FC<GalleryProps> = ({ message }) => {
  const { channel } = useChannelContext();
  const { isMyMessage } = useMessageContext();

  const projectId = channel?.data?.shape_project_id as ProjectSchema['id'];
  const { data: location } = useGetApiProjectsProjectIdLocationsLocationId(
    projectId,
    message?.attachments_location_id as string
  );

  return (
    <View className="flex">
      <StreamChatGallery additionalPressableProps={{ className: 'flex-1' }} />
      {location?.name && (
        <View
          className={cn(
            'my-2 pl-2 pr-3 py-1 z-10 bg-accent-indigo-bold flex flex-row items-center justify-center self-start gap-1 w-auto rounded-2xl',
            { 'bg-neutral-bold': !isMyMessage }
          )}
        >
          <MapPinIcon className="color-brand-inverse h-4 w-4" />
          <Text className="color-brand-inverse font-medium text-sm">{location.name}</Text>
        </View>
      )}
    </View>
  );
};
