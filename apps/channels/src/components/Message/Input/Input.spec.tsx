import { useActionSheet } from '@expo/react-native-action-sheet';
import { useAttachmentPickerActions } from 'src/components/AttachmentPicker/hooks/useAttachmentPickerActions';
import { channelContextFactory } from 'src/get-stream/test/factories/chat-context';
import { act, render, renderRouter, screen, userEvent, waitFor } from 'src/tests/test-utils';
import type { MessageInputContextValue } from 'stream-chat-expo';
import { Input } from './Input';

const mockUseMessageInputContext: jest.Mock<Partial<MessageInputContextValue>> = jest.fn();
jest.mock('stream-chat-expo', () => ({
  ...jest.requireActual('stream-chat-expo'),
  useMessageInputContext: jest.fn(() => mockUseMessageInputContext()),
}));

jest.mock('@expo/react-native-action-sheet', () => ({
  useActionSheet: jest.fn(() => ({
    showActionSheetWithOptions: jest.fn(),
  })),
}));
const useActionSheetMocked = jest.mocked(useActionSheet);
jest.mock('src/components/AttachmentPicker/hooks/useAttachmentPickerActions', () => ({
  useAttachmentPickerActions: jest.fn(() => ({
    camera: jest.fn(),
    gallery: jest.fn(),
    documents: jest.fn(),
  })),
}));
const useAttachmentPickerActionsMocked = jest.mocked(useAttachmentPickerActions);
jest.mock('src/config/environment', () => ({
  environment: {
    ...jest.requireActual('src/config/environment').environment,
    FEATURE_FLAG_UPLOAD_PREVIEW_SCREEN: true,
  },
}));

describe('<Input />', () => {
  beforeEach(() => {
    jest.restoreAllMocks();
  });

  describe('when feature flag is enabled', () => {
    describe('when there is no text in the input', () => {
      it('renders the camera icon button', async () => {
        mockUseMessageInputContext.mockReturnValue({
          text: '',
          isValidMessage: () => true,
        });

        render(<Input />);

        expect(await screen.findByRole('button', { name: 'camera' })).toBeOnTheScreen();
      });
    });

    describe('when there is text in the input', () => {
      it('renders the send button', async () => {
        mockUseMessageInputContext.mockReturnValue({
          text: 'Hello world',
          isValidMessage: () => true,
        });

        render(<Input />);

        expect(await screen.findByRole('button', { name: 'send message' })).toBeOnTheScreen();
      });
    });

    it('takes a picture, uploads and navigates to the upload preview screen', async () => {
      jest.useFakeTimers();
      const uploadNewImageMocked = jest.fn(() => Promise.resolve());
      mockUseMessageInputContext.mockReturnValue({
        text: '',
        isValidMessage: () => false,
        uploadNewImage: uploadNewImageMocked,
      });
      useAttachmentPickerActionsMocked.mockReturnValue({
        camera: () => Promise.resolve({ assets: [{ uri: 'uri' }] }),
      } as ReturnType<typeof useAttachmentPickerActions>);
      renderRouter(
        {
          '/channel/[channelId]': () => <Input />,
        },
        { initialUrl: '/channel/channel-1' }
      );

      await userEvent.press(screen.getByRole('button', { name: 'camera' }));

      await waitFor(() => {
        expect(screen).toHavePathname('/channel/messaging:channel-1/upload-preview');
      });
    });
  });

  describe('when attachment button is clicked', () => {
    describe('when is on a group or team chat', () => {
      it('shows the 4 available action options', async () => {
        jest.useFakeTimers();
        mockUseMessageInputContext.mockReturnValue({
          text: '',
          isValidMessage: () => true,
        });
        const showActionSheetWithOptionsMocked = jest.fn();
        useActionSheetMocked.mockReturnValue({
          showActionSheetWithOptions: showActionSheetWithOptionsMocked,
        });
        render(<Input />);

        await userEvent.press(await screen.findByRole('button', { name: 'attachment options' }));

        expect(showActionSheetWithOptionsMocked).toHaveBeenCalledWith(
          {
            cancelButtonIndex: 4,
            containerStyle: { paddingBottom: 0 },
            destructiveButtonIndex: 4,
            options: [
              'channel.mediaOptions.photos',
              'channel.mediaOptions.camera',
              'channel.mediaOptions.documents',
              'channel.actions.quickIssue',
              'actions.cancel',
            ],
          },
          expect.any(Function)
        );
      });
    });

    describe('when is on a 1:1 chat', () => {
      it('shows the 3 available action options', async () => {
        jest.useFakeTimers();
        mockUseMessageInputContext.mockReturnValue({
          text: '',
          isValidMessage: () => true,
        });
        const showActionSheetWithOptionsMocked = jest.fn();
        useActionSheetMocked.mockReturnValue({
          showActionSheetWithOptions: showActionSheetWithOptionsMocked,
        });
        renderRouter(
          {
            '/channel/[channelId]': () => <Input />,
          },
          { initialUrl: '/channel/channel-1' },
          {
            stream: {
              channel: channelContextFactory({
                id: 'channel-1',
                type: 'messaging',
                data: {
                  shape_project_id: undefined,
                },
              }),
            },
          }
        );

        await userEvent.press(await screen.findByRole('button', { name: 'attachment options' }));

        expect(showActionSheetWithOptionsMocked).toHaveBeenCalledWith(
          {
            cancelButtonIndex: 3,
            containerStyle: { paddingBottom: 0 },
            destructiveButtonIndex: 3,
            options: [
              'channel.mediaOptions.photos',
              'channel.mediaOptions.camera',
              'channel.mediaOptions.documents',
              'actions.cancel',
            ],
          },
          expect.any(Function)
        );
      });
    });

    describe('when attachment button is clicked', () => {
      it.each([
        ['gallery', 0],
        ['camera', 1],
        ['documents', 2],
      ])('upload from %s channels information without projects information', async (_option, index) => {
        jest.useFakeTimers();
        mockUseMessageInputContext.mockReturnValue({
          text: '',
          isValidMessage: () => true,
        });
        useAttachmentPickerActionsMocked.mockReturnValue({
          camera: () => Promise.resolve({ canceled: false }),
          gallery: () => Promise.resolve({ canceled: false }),
          documents: () => Promise.resolve({ canceled: false }),
        } as ReturnType<typeof useAttachmentPickerActions>);
        const showActionSheetWithOptionsMocked = jest.fn();
        useActionSheetMocked.mockReturnValue({
          showActionSheetWithOptions: showActionSheetWithOptionsMocked,
        });
        renderRouter(
          {
            '/channel/[channelId]': () => <Input />,
          },
          { initialUrl: '/channel/channel-1' }
        );

        await userEvent.press(await screen.findByRole('button', { name: 'attachment options' }));
        act(() => {
          showActionSheetWithOptionsMocked.mock.calls[0][1](index);
        });

        await waitFor(() => {
          expect(screen).toHavePathname('/channel/messaging:channel-1/upload-preview');
        });
      });
    });
  });
});
