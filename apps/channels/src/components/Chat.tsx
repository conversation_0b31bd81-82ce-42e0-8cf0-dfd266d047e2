import type { PropsWithChildren } from 'react';
import { ActivityIndicator, View } from 'react-native';
import FastImage from 'react-native-fast-image';
import { useChatClient } from 'src/get-stream/ChatClientProvider';
import { OverlayProvider, Chat as StreamChat } from 'stream-chat-expo';
import { useStreamTheme } from '../stream-theme';

export const Chat = ({ children }: PropsWithChildren) => {
  const { client } = useChatClient();
  const streamThemeObject = useStreamTheme();

  if (!client)
    return (
      <View className="flex-1 flex flex-col justify-center">
        <ActivityIndicator />
      </View>
    );

  return (
    <OverlayProvider value={{ style: streamThemeObject }}>
      <StreamChat
        client={client}
        enableOfflineSupport
        // @ts-expect-error
        ImageComponent={FastImage}
      >
        {children}
      </StreamChat>
    </OverlayProvider>
  );
};
