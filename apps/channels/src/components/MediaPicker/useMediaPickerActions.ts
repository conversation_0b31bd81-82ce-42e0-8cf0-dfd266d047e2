import { Toast } from '@shape-construction/arch-ui-native';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import * as Location from 'expo-location';
import { useTranslation } from 'react-i18next';

type CancelableAction<T> = Promise<(T & { canceled: false }) | { canceled: true }>;

export const useMediaPickerActions = () => {
  const { t } = useTranslation();

  const camera = async (options?: ImagePicker.ImagePickerOptions): CancelableAction<ImagePicker.ImagePickerResult> => {
    const permissionCameraResult = await ImagePicker.requestCameraPermissionsAsync();
    if (!permissionCameraResult.granted) {
      Toast.show(t('permissions.camera.denied'));
      return { canceled: true };
    }

    const locationPermissionResult = await Location.requestForegroundPermissionsAsync();
    if (!locationPermissionResult.granted) Toast.show(t('permissions.location.denied'));

    return ImagePicker.launchCameraAsync({
      allowsMultipleSelection: true,
      mediaTypes: ['images'],
      exif: true,
      orderedSelection: true,
      ...options,
    });
  };

  const gallery = async (options?: ImagePicker.ImagePickerOptions): CancelableAction<ImagePicker.ImagePickerResult> => {
    const permissionMediaLibraryResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (!permissionMediaLibraryResult.granted) {
      Toast.show(t('permissions.mediaLibrary.denied'));
      return { canceled: true };
    }

    return ImagePicker.launchImageLibraryAsync({
      allowsMultipleSelection: true,
      mediaTypes: ['images', 'videos'],
      exif: true,
      orderedSelection: true,
      ...options,
    });
  };

  const documents = async (options?: DocumentPicker.DocumentPickerOptions) => {
    return DocumentPicker.getDocumentAsync({ multiple: true, ...options });
  };

  return { camera, gallery, documents };
};
