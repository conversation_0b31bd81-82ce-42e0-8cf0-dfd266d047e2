import { useMemo, useState } from 'react';
import { Avatar, cn, List } from '@shape-construction/arch-ui-native';
import { UsersIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { useTranslation } from 'react-i18next';
import { FlatList, Text, TouchableOpacity, View } from 'react-native';
import Animated, { Easing, FadeInUp } from 'react-native-reanimated';
import {
  type MessageUserReactionsProps,
  type ReactionData,
  useChatContext,
  useMessageContext,
  useMessagesContext,
} from 'stream-chat-expo';
import { Icon } from '../Message/MessageSimple/ReactionsList/ReactionListBottom';
import type { ReactionObject } from '../Message/ReactionsSelector/reactionData';

type MessageUserReactionsItemProps = {
  image: string;
  name: string;
  reactionType: string;
  toRemove?: boolean;
  supportedReactionTypes: ReactionData[];
};

const MessageUserReactionsItem: React.FC<MessageUserReactionsItemProps> = ({
  image,
  name,
  reactionType,
  toRemove = false,
  supportedReactionTypes,
}) => {
  const { t } = useTranslation();

  return (
    <>
      <Avatar.Root size="md" alt="Image avatar">
        <Avatar.Image source={{ uri: image }} />
        <Avatar.Fallback className="bg-neutral">
          <Avatar.FallbackIcon Icon={UsersIcon} className="text-icon-neutral-inverse" />
        </Avatar.Fallback>
      </Avatar.Root>
      <List.Content>
        <List.Title className="text-sm leading-5 font-normal text-neutral">{name} </List.Title>
        {toRemove && (
          <Text className="text-xs leading-4 font-normal text-neutral-subtlest">
            {t('channel.messages.reactions.listPanel.tapToRemove')}
          </Text>
        )}
      </List.Content>
      <Icon size={20} supportedReactions={supportedReactionTypes} type={reactionType} />
    </>
  );
};

type MessageUserReactionsFilter = 'all' | ReactionObject['type'];

export const MessageUserReactions: React.FC<MessageUserReactionsProps> = (props) => {
  const { message } = props;
  const [selectedFilter, setSelectedFiler] = useState<MessageUserReactionsFilter>('all');
  const { client } = useChatContext();
  const { t } = useTranslation();
  const { handleToggleReaction } = useMessageContext();
  const { supportedReactions: supportedReactionTypes } = useMessagesContext();

  const { latest_reactions, reaction_counts } = message ?? {};

  const reactionTypes = useMemo(
    () => supportedReactionTypes?.map((supportedReactionType) => supportedReactionType.type) ?? [],
    [supportedReactionTypes]
  );

  const reactions = latest_reactions || [];

  const supportedReactions = useMemo(
    () => reactions.filter((reaction) => reactionTypes.includes(reaction.type)),
    [reactions, reactionTypes]
  );

  const reactionCounts = useMemo(
    () => (reaction_counts ? Object.entries(reaction_counts).filter(([type]) => reactionTypes.includes(type)) : []),
    [reaction_counts, reactionTypes]
  );

  const filteredSupportedReactions = useMemo(
    () => supportedReactions.filter((reaction) => (selectedFilter !== 'all' ? reaction.type === selectedFilter : true)),
    [supportedReactions, selectedFilter]
  );

  return (
    <>
      <View className="flex flex-row items-center">
        <TouchableOpacity accessibilityRole="button" onPress={() => setSelectedFiler('all')}>
          <View
            className={cn('py-1 px-4 ', {
              'bg-neutral-subtle rounded-xl': selectedFilter === 'all',
            })}
          >
            <Text className="text-sm leading-5 font-normal text-neutral-subtlest">
              {t('all')} {supportedReactions?.length}
            </Text>
          </View>
        </TouchableOpacity>

        <View className="flex flex-row gap-x-2 items-center">
          {reactionCounts.map(([type, count]) => (
            <TouchableOpacity
              key={type}
              accessibilityRole="button"
              onPress={() => setSelectedFiler(type as MessageUserReactionsFilter)}
            >
              <View
                className={cn('flex flex-row gap-x-1 items-center px-2 py-1', {
                  'bg-neutral-subtle rounded-xl': selectedFilter === type,
                })}
              >
                <Icon size={16} supportedReactions={supportedReactionTypes ?? []} type={type} />
                <Text className="text-sm leading-5 font-normal text-neutral-subtlest">{count}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      <List.Root accessible role="list" accessibilityRole="list" className="mt-2">
        <FlatList
          className="w-full"
          data={filteredSupportedReactions}
          renderItem={({ item: reaction }) => (
            <Animated.View
              key={`${reaction.type}-${reaction.user?.name}`}
              entering={FadeInUp.duration(250).easing(Easing.ease)}
            >
              <List.Item asChild>
                {reaction?.user?.id === client.userID ? (
                  <TouchableOpacity
                    accessibilityRole="button"
                    onPress={() => {
                      handleToggleReaction(reaction.type);
                      setSelectedFiler('all');
                    }}
                  >
                    <MessageUserReactionsItem
                      image={reaction.user?.image as string}
                      name={reaction.user?.name!}
                      reactionType={reaction.type}
                      supportedReactionTypes={supportedReactionTypes ?? []}
                      toRemove
                    />
                  </TouchableOpacity>
                ) : (
                  <View>
                    <MessageUserReactionsItem
                      image={reaction.user?.image!}
                      name={reaction.user?.name!}
                      reactionType={reaction.type}
                      supportedReactionTypes={supportedReactionTypes ?? []}
                    />
                  </View>
                )}
              </List.Item>
              <List.Divider />
            </Animated.View>
          )}
        />
      </List.Root>
    </>
  );
};
