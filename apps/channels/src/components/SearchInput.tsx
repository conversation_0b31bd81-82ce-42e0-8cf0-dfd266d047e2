import React, { type ComponentProps, type ElementType, useState } from 'react';
import { cn } from '@shape-construction/arch-ui-native';
import { MagnifyingGlassIcon } from '@shape-construction/arch-ui-native/src/Icons/outline';
import { TextInput, View } from 'react-native';

type SearchInputProps<T extends ElementType> = ComponentProps<T> & {
  as?: T;
};

export const SearchInput = <T extends ElementType = typeof TextInput>({ as, ...props }: SearchInputProps<T>) => {
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  const Component = as || TextInput;

  return (
    <View
      className={cn(
        'flex px-3 ios:py-1.5 flex-row gap-3 bg-neutral-subtle rounded-3xl border border-gray-300 items-center justify-start',
        {
          'border-accent-indigo': isFocused,
        }
      )}
    >
      <MagnifyingGlassIcon className="text-icon-neutral-subtle h-6 w-6" />
      <Component
        className="flex-1 placeholder:text-icon-neutral-subtle"
        clearButtonMode="always"
        onFocus={handleFocus}
        onBlur={handleBlur}
        {...props}
      />
    </View>
  );
};
