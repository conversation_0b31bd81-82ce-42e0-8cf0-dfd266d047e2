import React, { Fragment, useMemo } from 'react';
import { List, RadioGroup } from '@shape-construction/arch-ui-native';
import { FlatList, Text } from 'react-native';
import type { Node } from 'src/components/NodeList/types';
import { findNode, getNodeParentsPath } from 'src/components/NodeList/utils';
import Footer from './Footer';
import { useLocationPickerBottomSheetContext } from './LocationPickerBottomSheetContext';
import { RecentLocation } from './RecentLocation';

const LocationItem: React.FC<{ item: Node }> = ({ item }) => {
  const { nodes } = useLocationPickerBottomSheetContext();

  const parentsPath = useMemo(() => {
    const node = findNode(item.id, nodes);
    const path = node && getNodeParentsPath(node, nodes);
    return path;
  }, [nodes]);

  return (
    <Fragment key={item.id}>
      <List.Item>
        <RadioGroup.Item aria-label={item.name} value={item.id} className="flex-row-reverse">
          <List.Content>
            <RadioGroup.Label>
              <Text className="text-sm font-normal text-neutral-subtle">
                {parentsPath}
                <Text className="text-sm font-bold color-brand">{item.name}</Text>
              </Text>
            </RadioGroup.Label>
          </List.Content>
        </RadioGroup.Item>
      </List.Item>
      <List.Divider />
    </Fragment>
  );
};

export const LocationsList = () => {
  const { searchNodes, currentNode, handlePressOption, searchText, nodes } = useLocationPickerBottomSheetContext();
  const data = searchText ? searchNodes : nodes;

  return (
    <RadioGroup.Root value={currentNode.id} onValueChange={handlePressOption}>
      <FlatList
        testID="locationsList"
        ListFooterComponent={Footer}
        ListHeaderComponent={RecentLocation}
        data={data ?? []}
        renderItem={(itemProps) => {
          return <LocationItem {...itemProps} />;
        }}
      />
    </RadioGroup.Root>
  );
};

export default LocationsList;
