import { useActionSheet } from '@expo/react-native-action-sheet';
import { usePatchApiProjectsProjectIdGroupsGroupIdMembers } from '@shape-construction/api/src/hooks';
import { Toast } from '@shape-construction/arch-ui-native';
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { Alert } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type { ChannelMemberResponse } from 'stream-chat';
import { useChannelContext, useChatContext } from 'stream-chat-expo';
import { useChannelMembersQueryOptions } from './useChannelMembers';

export const useRemoveMemberConfirmation = (selectedMember?: ChannelMemberResponse) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { channel } = useChannelContext();
  const { client } = useChatContext();
  const { showActionSheetWithOptions } = useActionSheet();
  const { bottom: bottomInset } = useSafeAreaInsets();

  const channelMembersQueryOptions = useChannelMembersQueryOptions(channel);
  const patchApiProjectsProjectIdGroupsGroupIdMembersMutation = usePatchApiProjectsProjectIdGroupsGroupIdMembers();

  const { data: members } = useInfiniteQuery(channelMembersQueryOptions);
  const memberMe = members?.find((member) => member.user_id === client.user!.id);

  const toggleRemoveMember = () => {
    showActionSheetWithOptions(
      {
        title: t('channel.details.removeMembers.removeMemberConfirmation.title'),
        message: t('channel.details.removeMembers.removeMemberConfirmation.description', {
          member: selectedMember?.user?.name,
        }),
        cancelButtonIndex: 1,
        destructiveButtonIndex: 0,
        options: [t('channel.details.removeMembers.removeMemberConfirmation.confirmRemove'), t('actions.cancel')],
        containerStyle: { paddingBottom: bottomInset },
      },
      async (buttonIndex) => {
        if (buttonIndex === 0) {
          onRemoveMember();
        }
      }
    );
  };

  const onRemoveMember = async () => {
    if (!selectedMember) return;

    try {
      const message = t('channel.details.removeMembers.notifications.removeMemberSuccess', {
        username: memberMe?.user?.name,
        member: selectedMember?.user?.name,
      });

      await patchApiProjectsProjectIdGroupsGroupIdMembersMutation.mutateAsync({
        projectId: channel.data?.shape_project_id!,
        groupId: channel.data?.shape_group_id!,
        data: {
          remove_user_ids: [selectedMember?.user?.shape_user_id!],
        },
      });
      await queryClient.invalidateQueries(channelMembersQueryOptions);
      Toast.show(message);
    } catch {
      Alert.alert(t('channel.details.removeMembers.notifications.removeMemberError'));
    }
  };

  return {
    selectedMember,
    toggleRemoveMember,
  };
};
