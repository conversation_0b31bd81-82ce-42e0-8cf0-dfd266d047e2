import React, { type ComponentProps, type ReactElement } from 'react';
import { render as renderRtl, screen, userEvent, waitFor } from 'tests/test-utils';
import { MediaCarousel } from './MediaCarousel';

const images = Array.from(Array(3).keys(), (index) => ({
  canEdit: index < 2,
  canDelete: index < 2,
  caption: index >= 1 ? `fantastic caption ${index}` : undefined,
  downloadUrl: `http://intercept.me.shape/issue_image_${index}/download/300`,
  extension: 'jpg',
  filename: `issue_image${index}.jpg`,
  id: '00000000-72ba-408b-9aed-612abbe69a9e',
  mediaUrl: {
    large: `http://intercept.me.shape/issue_image_${index}/xx-large/300`,
    medium: `http://intercept.me.shape/issue_image_${index}/large/300`,
    thumbnail: `http://intercept.me.shape/issue_image_${index}/small/300`,
  },
}));

// ImageBox component needs a parent div to be rendered first in order to know where to render.
// Therefore, we need to ensure that MediaCarousel renders a first time with isImageBoxOpen as false
// so that the parent div already exists when isImageBoxOpen is true.
const render = (component: ReactElement<ComponentProps<typeof MediaCarousel>>) => {
  const view = renderRtl(component);

  view.rerender(React.cloneElement(component, { ...component.props, isImageBoxOpen: true }));
  return view;
};

describe('MediaCarousel', () => {
  describe('when isImageBoxOpen is false', () => {
    it('does not open image box', async () => {
      const defaultProps = {
        isImageBoxOpen: false,
        closeImageBox: jest.fn(),
        images,
        selectedImageIndex: 1,
        setImageIndex: jest.fn(),
        imagesLoading: false,
        uploadImage: jest.fn(),
        deleteImage: jest.fn(),
        updateImage: jest.fn(),
      };

      renderRtl(<MediaCarousel {...defaultProps} isImageBoxOpen={false} />);

      await waitFor(() => expect(screen.queryByRole('dialog', { name: 'Lightbox' })).toBeNull());
    });
  });

  describe('when isImageBoxOpen is true', () => {
    it('renders properly', async () => {
      const defaultProps = {
        isImageBoxOpen: false,
        closeImageBox: jest.fn(),
        images,
        selectedImageIndex: 1,
        setImageIndex: jest.fn(),
        imagesLoading: false,
        uploadImage: jest.fn(),
        deleteImage: jest.fn(),
        updateImage: jest.fn(),
      };

      render(<MediaCarousel {...defaultProps} />);

      expect(await screen.findByRole('dialog', { name: 'Lightbox' })).toBeInTheDocument();

      expect(screen.getByLabelText('Download image')).toBeInTheDocument();
      expect(screen.getByLabelText('Download image')).toHaveAttribute(
        'href',
        'http://intercept.me.shape/issue_image_1/download/300'
      );
      expect(screen.getByLabelText('Delete image')).toBeInTheDocument();
      expect(screen.getByLabelText('Edit image')).toBeInTheDocument();
      expect(screen.getByLabelText('Edit caption')).toBeInTheDocument();
      expect(screen.getByLabelText('Zoom in')).toBeInTheDocument();
      expect(screen.getByLabelText('Zoom out')).toBeInTheDocument();
      expect(screen.getByLabelText('Close lightbox')).toBeInTheDocument();

      expect(await screen.findByText('fantastic caption 1')).toBeInTheDocument();
    });

    describe('when the user cannot edit the caption of an image', () => {
      it('does not render the edit caption button on image preview', async () => {
        const defaultProps = {
          isImageBoxOpen: false,
          closeImageBox: jest.fn(),
          images,
          selectedImageIndex: 1,
          setImageIndex: jest.fn(),
          imagesLoading: false,
          uploadImage: jest.fn(),
          deleteImage: jest.fn(),
          updateImage: jest.fn(),
        };
        render(<MediaCarousel {...defaultProps} selectedImageIndex={2} />);

        await waitFor(() => expect(screen.queryByLabelText('Edit caption')).not.toBeInTheDocument());
      });
    });

    describe('when click on the edit caption button on image preview', () => {
      describe('and update the image caption', () => {
        it('renders image preview with new caption', async () => {
          const defaultProps = {
            isImageBoxOpen: false,
            closeImageBox: jest.fn(),
            images,
            selectedImageIndex: 1,
            setImageIndex: jest.fn(),
            imagesLoading: false,
            uploadImage: jest.fn(),
            deleteImage: jest.fn(),
            updateImage: jest.fn(),
          };
          render(<MediaCarousel {...defaultProps} />);

          await userEvent.click(await screen.findByLabelText('Edit caption'));
          await userEvent.clear(await screen.findByLabelText('imageCaptionModal.caption'));
          await userEvent.type(await screen.findByLabelText('imageCaptionModal.caption'), 'new_caption');
          await userEvent.click(await screen.findByLabelText('update-edit-caption'));

          await waitFor(() => expect(defaultProps.updateImage).toHaveBeenCalled());
        });
      });
    });

    describe('image editor', () => {
      it('opens markup editor when click edit button on image preview', async () => {
        const defaultProps = {
          isImageBoxOpen: false,
          closeImageBox: jest.fn(),
          images,
          selectedImageIndex: 1,
          setImageIndex: jest.fn(),
          imagesLoading: false,
          uploadImage: jest.fn(),
          deleteImage: jest.fn(),
          updateImage: jest.fn(),
        };
        render(<MediaCarousel {...defaultProps} />);

        await userEvent.click(screen.getByLabelText('Edit image'));
        expect(screen.getByLabelText('Edit image')).toBeInTheDocument();
      });

      it('closes markup editor', async () => {
        const defaultProps = {
          isImageBoxOpen: false,
          closeImageBox: jest.fn(),
          images,
          selectedImageIndex: 1,
          setImageIndex: jest.fn(),
          imagesLoading: false,
          uploadImage: jest.fn(),
          deleteImage: jest.fn(),
          updateImage: jest.fn(),
        };
        render(<MediaCarousel {...defaultProps} />);

        await userEvent.click(screen.getByLabelText('Edit image'));
        expect(screen.getByLabelText('Edit image')).toBeInTheDocument();

        await userEvent.click(await screen.findByLabelText('Close Overlay'));
        await waitFor(() => expect(defaultProps.closeImageBox).toHaveBeenCalled());
      });
    });
  });
});
