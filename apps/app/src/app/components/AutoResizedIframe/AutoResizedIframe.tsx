import { type ComponentProps, forwardRef, type SyntheticEvent, useRef } from 'react';
import { initialize } from '@open-iframe-resizer/core';
import { composeRefs } from '@radix-ui/react-compose-refs';
import { useStableCallback } from '@shape-construction/hooks';

export type AutoResizedIframeProps = ComponentProps<'iframe'>;

/**
 * Uses open-iframe-resizer to scale the iframe dimensions to the contents inside the iframe
 *
 * @remarks
 * Requires contents inside iframe to load and initialize iframe-resizer or open-iframe-resizer
 */
export const AutoResizedIframe = forwardRef<HTMLIFrameElement, AutoResizedIframeProps>((props, ref) => {
  const resizerIframeRef = useRef<HTMLIFrameElement | null>(null);

  const onLoad = useStableCallback((event: SyntheticEvent<HTMLIFrameElement, Event>) => {
    initialize({ enableLegacyLibSupport: true }, resizerIframeRef.current!);
    props.onLoad?.(event);
  });

  return <iframe ref={composeRefs(ref, resizerIframeRef)} onLoad={onLoad} {...props} />;
});

AutoResizedIframe.displayName = 'AutoResizedIframe';
