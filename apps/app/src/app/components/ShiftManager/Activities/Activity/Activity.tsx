import React from 'react';
import type { ProjectSchema, ShiftActivitySchema } from '@shape-construction/api/src/types';
import Drawer from '@shape-construction/arch-ui/src/Drawer';
import { useSuspenseQuery } from '@tanstack/react-query';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { useShiftActivity } from 'app/queries/activities/activities';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { EditActivity } from 'app/screens/Project/ProjectShiftManager/Activities/EditActivity/EditActivity';
import { useLocation, useNavigate, useParams } from 'react-router';

type Params = {
  projectId: ProjectSchema['id'];
  shiftActivityId: ShiftActivitySchema['id'];
  tabId?: string;
};

export const Activity: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { projectId, shiftActivityId } = useParams<Params>() as Params;
  const { data: project } = useSuspenseQuery(getProjectQueryOptions(projectId));
  const { data: shiftActivity, isLoading } = useShiftActivity(projectId!, shiftActivityId!);

  const projectTimezone = project?.timezone!;

  const onClose = () => {
    const backgroundPathname = location.state?.background?.pathname ?? location.state?.background;
    const navigatePath = backgroundPathname || `/projects/${projectId}/activities`;

    navigate(navigatePath);
  };

  if (!shiftActivity) return null;
  if (isLoading) return <LoadingSpinner variant="screen" />;

  return (
    <Drawer.Root
      open
      onClose={onClose}
      maxWidth="lg"
      zIndex={10}
      data-testid="activity-details"
      data-cy="activity-details"
    >
      <EditActivity
        projectId={projectId}
        shiftActivityId={shiftActivityId}
        onClose={onClose}
        timezone={projectTimezone}
      />
    </Drawer.Root>
  );
};

export { Activity as Component };
