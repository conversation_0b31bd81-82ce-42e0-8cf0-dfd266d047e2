import React from 'react';
import { activityFactory } from '@shape-construction/api/factories/activities';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-activities';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { render, screen, userEvent } from 'tests/test-utils';
import { Activity } from './Activity';

describe('Activity', () => {
  describe('close button', () => {
    describe('when has background location', () => {
      it('redirects to background location', async () => {
        const project = projectFactory({ id: 'project-0' });
        const shiftActivity = activityFactory({ id: 'activity-0', archived: true });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler(() => shiftActivity)
        );
        const history = createMemoryHistory({
          initialEntries: [
            {
              pathname: '/projects/project-0/activities/activity-0',
              state: {
                background: {
                  pathname: '/projects/project-0/activities/archived',
                },
              },
            },
          ],
        });
        const route = { path: '/projects/:projectId/activities/:shiftActivityId' };
        render(<Activity />, { history, route });

        await userEvent.click(await screen.findByRole('button', { name: 'Close Overlay' }));

        expect(history.location.pathname).toBe('/projects/project-0/activities/archived');
      });
    });

    describe('when does not have background location', () => {
      it('redirects to activities list', async () => {
        const project = projectFactory({ id: 'project-0' });
        const shiftActivity = activityFactory({ id: 'activity-0' });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler(() => shiftActivity)
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/activities/activity-0'],
        });
        const route = { path: '/projects/:projectId/activities/:shiftActivityId' };
        render(<Activity />, { history, route });

        await userEvent.click(await screen.findByRole('button', { name: 'Close Overlay' }));

        expect(history.location.pathname).toBe('/projects/project-0/activities');
      });
    });
  });
});
