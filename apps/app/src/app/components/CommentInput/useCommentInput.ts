import { useCallback, useContext, useRef } from 'react';
import { useMessage } from '@messageformat/react';
import { type Editor, type EditorOptions, type JSONContent, useEditor } from '@tiptap/react';
import { atom, useAtom } from 'jotai';
import { atomFamily } from 'jotai/utils';
import { buildEditorExtensions } from './editorExtensions';
import type { MentionsListCommandsHandler } from './MentionsList';
import { TestEditorContext } from './TestUtils/FakeTipTapEditorContextProvider';

export type useCommentInputProps = {
  id: string;
  onFocus: () => void;
  onSubmit: (editor: Editor) => void;
};

const textPerChannelPerIssueAtom = atomFamily((id: string) => atom<JSONContent | null>(null));

export const useCommentInput = ({ id, onFocus, onSubmit }: useCommentInputProps) => {
  const [text, setText] = useAtom(textPerChannelPerIssueAtom(id));
  const mentionsRef = useRef<MentionsListCommandsHandler>(null);
  const placeholderMessage = useMessage('issue.detail.activity.input.placeholder');
  const { hijackEditorForTests } = useContext(TestEditorContext);

  const onUpdate: EditorOptions['onUpdate'] = useCallback(
    ({ editor: editorInstance }) => {
      setText?.(editorInstance.getJSON());
    },
    [setText]
  );

  const handleSubmit = useCallback(
    (editor: Editor) => {
      if (editor.getText().trim()) {
        onSubmit(editor);
        setText(null);
        editor.commands.clearContent();
      }
    },
    [onSubmit, setText]
  );

  const editor = useEditor(
    {
      content: text,
      onUpdate,
      onBeforeCreate: hijackEditorForTests as any,
      onFocus,
      extensions: buildEditorExtensions({
        placeholderMessage,
        mentionsRef: mentionsRef as React.RefObject<MentionsListCommandsHandler>,
        handleSubmit,
      }),
      editorProps: {
        attributes: {
          role: 'textbox',
          'data-cy': 'comment-input',
          class:
            'max-h-40 overflow-y-auto bg-white px-3 py-2 border rounded-md shadow-xs focus:outline-hidden border-gray-300 focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 text-sm',
        },
      },
    },
    [onFocus]
  );

  return {
    editor,
    onSubmit: () => handleSubmit(editor!),
    mentionsRef,
  };
};
