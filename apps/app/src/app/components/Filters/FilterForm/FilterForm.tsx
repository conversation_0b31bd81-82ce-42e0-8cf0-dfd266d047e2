import React, { memo, type PropsWithChildren, useEffect, useMemo } from 'react';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import {
  type DeepPartial,
  type FieldValues,
  FormProvider,
  type UseFormReturn,
  useForm,
  useFormContext as useReactHookFormContext,
} from 'react-hook-form';
import { getAppliedFiltersCount } from '../utils';

export type FilterFormProps<T extends FieldValues> = PropsWithChildren<{
  defaultValues: DeepPartial<T>;
  values: T;
  onSubmit: (values: T) => void;
  /**
   * Fields that should trigger an automatic form submission when their values change.
   * If the screen size is large, the fields in `large` will be used, otherwise the fields in `small`.
   */
  autoSubmitFields?: {
    large?: Partial<keyof T>[];
    small?: Partial<keyof T>[];
  };
}>;

export type ExtendedUseFormReturn<T extends FieldValues = FieldValues> = UseFormReturn<T> & {
  defaultValues: DeepPartial<T>;
};

export const FilterFormComponent = <T extends FieldValues>({
  children,
  defaultValues,
  values,
  onSubmit,
  autoSubmitFields,
}: FilterFormProps<T>) => {
  const form = useForm<T>({ defaultValues, values }) as ExtendedUseFormReturn<T>;
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const watchKeys = useMemo(
    () => (isLargeScreen ? autoSubmitFields?.large : autoSubmitFields?.small),
    [isLargeScreen, autoSubmitFields]
  );

  const { watch, handleSubmit } = form;
  useEffect(() => {
    const subscription = watch((_, { name }) => {
      if (watchKeys?.includes(name as keyof T)) {
        handleSubmit(onSubmit)();
      }
    });

    return () => subscription.unsubscribe();
  }, [handleSubmit, onSubmit, watch, watchKeys]);

  form.defaultValues = defaultValues;

  return <FormProvider {...form}>{children}</FormProvider>;
};

export const FilterForm = memo(FilterFormComponent) as <T extends FieldValues>(
  props: FilterFormProps<T>
) => React.ReactElement;

export const useFormFiltersContext = <T extends Record<string, any>>() =>
  useReactHookFormContext<T>() as ExtendedUseFormReturn;

export const useFormFiltersCount = <T extends Record<string, any>>() => {
  const form = useFormFiltersContext<T>();
  const params = form.watch();
  const { search, selected_ids, multi_select, ...filteredParams } = params;

  return useMemo(() => getAppliedFiltersCount(filteredParams), [params]);
};
