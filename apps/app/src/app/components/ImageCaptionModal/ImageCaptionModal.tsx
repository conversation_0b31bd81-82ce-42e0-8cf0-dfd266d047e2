import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import InputText from '@shape-construction/arch-ui/src/InputText';
import Modal from '@shape-construction/arch-ui/src/ModalBase';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { Form, Formik } from 'formik';
import FieldInputWrapper from '../Formik/FieldInputWrapper';

export type ImageCaptionModalProps = {
  caption: string;
  filename: string;
  filetype: string;
  onClose: () => void;
  onUpdate: (values: any) => void;
  thumbnailUrl: string;
};

const ImageCaptionModal: React.FC<ImageCaptionModalProps> = ({
  caption,
  filename,
  filetype,
  onClose,
  onUpdate,
  thumbnailUrl,
}) => {
  const messages = useMessageGetter('imageCaptionModal');
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const [filenameString, filenameExtension] = filename.split('.');

  return (
    <Formik initialValues={{ caption }} onSubmit={onUpdate}>
      {({ isSubmitting, submitForm }) => (
        <Form placeholder="" onPointerEnterCapture={() => {}} onPointerLeaveCapture={() => {}}>
          <Modal.Root
            onClose={onClose}
            maxWidth={isLargeScreen ? 'lg' : 'none'}
            fullScreen={!isLargeScreen}
            open
            roundBorders={isLargeScreen}
          >
            <Modal.Header onClose={onClose} bottomBorder>
              <Modal.Title>{messages('title')}</Modal.Title>
            </Modal.Header>
            <Modal.Content className="flex space-y-8 pt-6 pb-6">
              <div className="flex flex-col space-y-6 lg:flex-row lg:space-y-0 lg:space-x-4 lg:overflow-hidden">
                <img className="w-32 rounded-sm lg:shrink-0" alt="edit-caption" src={thumbnailUrl} />
                <dl className="flex w-full flex-col space-y-1 lg:overflow-hidden">
                  <dt className="flex w-full overflow-hidden">
                    <dfn className="shrink-0 font-semibold not-italic">{messages('filename')}:&nbsp;</dfn>
                    <span className="truncate font-light">{filenameString}</span>
                    <span className="font-light">.{filenameExtension}</span>
                  </dt>
                  <dt className="flex">
                    <dfn className="font-semibold not-italic">{messages('filetype')}:&nbsp;</dfn>
                    <span className="font-light">{filetype}</span>
                  </dt>
                </dl>
              </div>
              <div>
                <FieldInputWrapper name="caption" Input={InputText} label={messages('caption')} setTouchedOnChange />
              </div>
            </Modal.Content>
            <Modal.Footer>
              <Button
                color="secondary"
                variant="outlined"
                size="md"
                onClick={onClose}
                aria-label={messages('cancelCTA')}
              >
                {messages('cancelCTA')}
              </Button>
              <Button
                color="primary"
                variant="contained"
                size="md"
                disabled={isSubmitting}
                type="submit"
                onClick={submitForm}
                aria-label="update-edit-caption"
              >
                {messages('updateCTA')}
              </Button>
            </Modal.Footer>
          </Modal.Root>
        </Form>
      )}
    </Formik>
  );
};

export default ImageCaptionModal;
