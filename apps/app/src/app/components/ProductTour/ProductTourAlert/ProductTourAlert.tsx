import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import { getApiProductToursProductTourKeyQueryOptions } from '@shape-construction/api/src/hooks';
import Alert from '@shape-construction/arch-ui/src/Alert';
import Button from '@shape-construction/arch-ui/src/Button';
import { PlayCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { useQueryClient } from '@tanstack/react-query';
import type { AnalyticEvent, StorylaneEventName } from 'app/analytics/eventsTypes';
import { useSendAnalyticsEvent } from 'app/queries/analytics/analytics';
import { useProductTourUpdate } from 'app/queries/productTours/productTours';
import { useProductTourPopover } from '../hooks/useProductTourPopover';

export type StorylaneAnalyticEvent = AnalyticEvent<StorylaneEventName>;

interface ProductTourAlertProps {
  openStorylaneModal: () => void;
  productTourKey: string;
  productTourName: string | React.ReactElement;
  showCloseButton: boolean;
  displayInRow: boolean;
}

export const ProductTourAlert: React.FC<ProductTourAlertProps> = ({
  openStorylaneModal,
  productTourKey,
  productTourName,
  showCloseButton,
  displayInRow,
}) => {
  const queryClient = useQueryClient();
  const messages = useMessageGetter('productTour.alert');
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const { showProductTourPopover, openProductTourPopover } = useProductTourPopover();

  const { mutate: updateProductTour } = useProductTourUpdate();
  const { mutate: sendAnalyticsEvent } = useSendAnalyticsEvent();

  const handleCloseAlert = () => {
    if (showProductTourPopover) openProductTourPopover();

    sendAnalyticsEvent({
      event_name: 'tour_dismissed',
      properties: { tour_key: productTourKey },
    } as StorylaneAnalyticEvent);
    updateProductTour(
      { productTourKey, data: { dismissed_at: new Date().toISOString() } },
      {
        onSuccess: () => {
          queryClient.invalidateQueries(getApiProductToursProductTourKeyQueryOptions(productTourKey));
        },
      }
    );
  };

  const isFullWidthLayout = isLargeScreen || displayInRow;
  const containerClasses = isFullWidthLayout ? '' : 'm-2';

  return (
    <div className={containerClasses}>
      <Alert
        color="discovery"
        emphasis="bold"
        rounded={!isFullWidthLayout}
        customIcon={<PlayCircleIcon className="h-5 w-5 text-white" />}
        onClose={showCloseButton ? handleCloseAlert : undefined}
        justifyContent="start"
        displayInRow={displayInRow}
      >
        <Alert.Message>{messages('message', { productTourName })}</Alert.Message>
        <Alert.Actions>
          <Button variant="outlined" color="white" size="xs" onClick={openStorylaneModal}>
            {messages('viewTourCTA')}
          </Button>
        </Alert.Actions>
      </Alert>
    </div>
  );
};
