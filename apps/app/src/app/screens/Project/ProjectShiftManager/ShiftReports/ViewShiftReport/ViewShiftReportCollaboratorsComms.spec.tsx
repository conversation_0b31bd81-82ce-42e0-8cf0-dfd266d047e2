import React from 'react';
import { activeStorageFileFactory } from '@shape-construction/api/factories/activestorage';
import {
  shiftReportCommentAttachment,
  shiftReportCommentAvailableActionsFactory,
  shiftReportCommentFactory,
  shiftReportsAvailableActions,
  shiftReportsFactory,
} from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory, userFactory } from '@shape-construction/api/factories/users';
import { postApiDirectUploadsDocumentMockHandler } from '@shape-construction/api/handlers-factories/direct-upload';
import {
  deleteApiProjectsProjectIdShiftReportsShiftReportIdCommentsCommentIdMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdPeopleMockHandler,
  postApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import type {
  ShiftReportCommentSchema,
  ShiftReportSchema,
  TeamMemberListSchema,
  UserBasicDetailsSchema,
} from '@shape-construction/api/src/types';
import { FakeTipTapEditor } from 'app/components/CommentInput/TestUtils/FakeTipTapEditor';
import { createMemoryHistory } from 'history';
import { richTextFactory } from 'tests/factories/richTextFactory';
import { server } from 'tests/mock-server';
import { render, screen, waitFor, waitForElementToBeRemoved, within } from 'tests/test-utils';
import { ViewShiftReportCollaboratorsComms } from './ViewShiftReportCollaboratorsComms';

const setupTest = (
  data: {
    shiftReport?: ShiftReportSchema;
    comments?: ShiftReportCommentSchema[];
    teamMembers?: TeamMemberListSchema;
    user?: UserBasicDetailsSchema;
  } = {},
  renderContext: Parameters<typeof render>[0] = {}
) => {
  const shiftReport = data.shiftReport || shiftReportsFactory();
  const teamMembers = data.teamMembers || [teamMemberFactory()];
  const user = userFactory(data.user || {});
  let shiftReportComments = data.comments || [shiftReportCommentFactory({ richText: richTextFactory('Comment') })];

  server.use(
    postApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler(async ({ request }) => {
      const body = await request.json();
      const comment = shiftReportCommentFactory({
        richText: body.rich_text,
        plainText: body.plain_text,
        teamMemberId: teamMembers[0].id,
        attachments: body.signed_ids?.map(() => shiftReportCommentAttachment()),
      });
      shiftReportComments.unshift(comment);
      return comment;
    }),
    deleteApiProjectsProjectIdShiftReportsShiftReportIdCommentsCommentIdMockHandler(({ params }) => {
      const commentId = params.commentId;
      shiftReportComments = shiftReportComments.filter(({ id }) => id !== commentId);
    }),
    getApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler(() => ({
      comments: shiftReportComments,
      meta: {
        firstEntryCursor: 'firstEntryCursor',
        hasNextPage: false,
        hasPreviousPage: false,
        lastEntryCursor: 'lastEntryCursor',
        total: shiftReportComments.length,
      },
    })),
    getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
    getApiProjectsProjectIdPeopleMockHandler(() => teamMembers),
    getApiProjectsProjectIdShiftReportsShiftReportIdPeopleMockHandler(() => teamMembers),
    postApiDirectUploadsDocumentMockHandler(() => activeStorageFileFactory())
  );

  const history = createMemoryHistory({
    initialEntries: ['/projects/project-0/shift-reports/shift-report-0/collaborators'],
  });
  const route = {
    path: '/projects/:projectId/shift-reports/:shiftReportId/collaborators',
    context: { shiftReport },
  };

  return render(<ViewShiftReportCollaboratorsComms />, {
    user,
    history,
    route,
    ...renderContext,
  });
};

describe('ViewShiftReportCollaboratorsComms', () => {
  it('allow to send messages', async () => {
    const fakeEditor = new FakeTipTapEditor();
    const user = userBasicDetailsFactory();
    const teamMember = teamMemberFactory({ user });
    const teamMembers = [teamMember];

    const { user: userEvent } = setupTest({ comments: [], teamMembers, user }, { fakeEditor });
    await fakeEditor.type('A new comment');
    await waitFor(() => expect(fakeEditor.instance.getText()).toBe('A new comment'));
    await userEvent.click(await screen.findByRole('button', { name: 'submit comment' }));

    await waitFor(() => {
      expect(screen.getByText('A new comment')).toBeInTheDocument();
    });
  });

  describe('when there are comments', () => {
    it('list collaborator comments', async () => {
      const user = userBasicDetailsFactory();
      const teamMember = teamMemberFactory({ user });
      const teamMembers = [teamMember];
      const comments = [
        shiftReportCommentFactory({
          teamMemberId: teamMember.id,
          richText: richTextFactory('Third comment'),
        }),
        shiftReportCommentFactory({
          teamMemberId: teamMember.id,
          richText: richTextFactory('Second comment'),
        }),
        shiftReportCommentFactory({
          teamMemberId: teamMember.id,
          richText: richTextFactory('First comment'),
        }),
      ];

      setupTest({ comments, teamMembers, user });

      expect(await screen.findAllByRole('listitem', { name: 'comment' })).toHaveLength(3);
      expect(await screen.findByText('Third comment')).toBeInTheDocument();
      expect(await screen.findByText('Second comment')).toBeInTheDocument();
      expect(await screen.findByText('First comment')).toBeInTheDocument();
    });

    describe('when the user has permission to delete', () => {
      it('deletes the comment', async () => {
        const user = userBasicDetailsFactory();
        const teamMember = teamMemberFactory({ user });
        const teamMembers = [teamMember];
        const comments = [
          shiftReportCommentFactory({
            teamMemberId: teamMember.id,
            richText: richTextFactory('First comment'),
            availableActions: shiftReportCommentAvailableActionsFactory({ delete: true }),
          }),
        ];

        const { user: userEvent } = setupTest({ comments, teamMembers, user });

        await userEvent.click(await screen.findByRole('button', { name: 'shiftReport.comments.delete' }));
        await userEvent.click(
          await screen.findByRole('button', { name: 'issue.detail.activity.delete.confirmation.confirmCTA' })
        );

        await waitForElementToBeRemoved(screen.queryByText('shiftReport.comments.state.deleting'));
        await waitFor(() => expect(screen.queryAllByRole('listitem', { name: 'comment' })).toHaveLength(0));
      });
    });
  });

  describe('when there are not comments', () => {
    it('shows an empty message', async () => {
      setupTest({ comments: [] });

      expect(await screen.findByText('shiftReport.comments.empty.title')).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.comments.empty.message')).toBeInTheDocument();
    });
  });

  describe('when the user has no permission to edit', () => {
    it('does not display the upload images option', async () => {
      const user = userBasicDetailsFactory();

      setupTest({
        user,
        shiftReport: shiftReportsFactory({
          availableActions: shiftReportsAvailableActions({ edit: false }),
        }),
      });

      expect(await screen.findByRole('textbox')).toBeInTheDocument();
      expect(screen.queryByLabelText('upload files', { selector: 'input' })).not.toBeInTheDocument();
    });
  });

  it('shows the mentions list with the first highlighted', async () => {
    const fakeEditor = new FakeTipTapEditor();
    const teamMembers = [
      teamMemberFactory({
        id: 1,
        user: userBasicDetailsFactory({ name: 'John Doe' }),
      }),
      teamMemberFactory({
        id: 2,
        user: userBasicDetailsFactory({ name: 'Mary Jane' }),
      }),
    ];
    setupTest({ comments: [], teamMembers }, { fakeEditor });
    expect(await screen.findByRole('textbox')).toBeInTheDocument();

    await fakeEditor.type('@');

    const mentionsList = await screen.findByTestId('mentions-list');
    expect(mentionsList).toBeInTheDocument();
    expect(await waitFor(() => within(mentionsList).getByRole('option', { selected: true }))).toHaveTextContent(
      'John Doe'
    );
    expect(within(mentionsList).getByText('John Doe')).toBeInTheDocument();
    expect(within(mentionsList).getByText('Mary Jane')).toBeInTheDocument();
  });

  it('sends a message as comment into the chat feed', async () => {
    const fakeEditor = new FakeTipTapEditor();
    const { user } = setupTest({ comments: [] }, { fakeEditor });
    expect(await screen.findByRole('textbox')).toBeInTheDocument();

    await fakeEditor.type('This is a comment');

    const submitButton = screen.getByRole('button', { name: 'submit comment' });
    await waitFor(() => expect(submitButton).toBeEnabled());
    await user.click(submitButton);
    await waitFor(() => expect(screen.queryAllByRole('listitem', { name: 'comment' })).toHaveLength(1));
  });

  it('sends an image as comment into the chat feed', async () => {
    const fakeEditor = new FakeTipTapEditor();
    /**
     * By default, user-event has applyAccept defined as true,
     * but for some reason, when using * it is not being accepted
     */
    const { user } = setupTest({ comments: [] }, { fakeEditor });

    expect(await screen.findByRole('textbox')).toBeInTheDocument();

    const files = [
      new File(['photo-1'], 'filename-1.jpg', { type: 'image/jpg' }),
      new File(['photo-2'], 'filename-2.jpg', { type: 'image/jpg' }),
    ];
    await user.upload(await screen.findByLabelText('upload files', { selector: 'input' }), files);
    await waitFor(() => expect(screen.getAllByRole('img', { name: 'thumbnail-image' })).toHaveLength(2));
  });
});
