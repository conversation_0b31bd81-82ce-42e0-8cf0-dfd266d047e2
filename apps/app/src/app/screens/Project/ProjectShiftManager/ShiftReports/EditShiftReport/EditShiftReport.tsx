import React, { useEffect, useRef } from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import { CheckCircleIcon, DocumentMagnifyingGlassIcon, EyeIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { CheckIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Page from '@shape-construction/arch-ui/src/Page';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import * as Tooltip from '@shape-construction/arch-ui/src/Tooltip';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useMediaQuery } from '@shape-construction/hooks';
import { formatDateISO, now } from '@shape-construction/utils/DateTime';
import { useIsMutating } from '@tanstack/react-query';
import { PageSaveLoading } from 'app/components/PageSaveLoading/PageSaveLoading';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useCurrentShiftReport } from 'app/contexts/currentShiftReport';
import { useShiftReportsQualityIndicators } from 'app/queries/shiftReports/qualityIndicators/qualityIndicators';
import {
  usePreviousShiftReport,
  useShiftReport,
  useSubmitForReviewShiftReport,
  useUpdateShiftReport,
} from 'app/queries/shiftReports/shiftReports';
import { Navigate, useLocation, useNavigate } from 'react-router';
import { useTeamsSubscriptionPlan } from '../../../../../queries/teamsSubscriptionPlan/teamsSubscriptionPlan';
import { ShiftReportActionBar } from '../components/ShiftReportActionBar';
import { ShiftReportActionDropdown } from '../components/ShiftReportActionDropdown';
import { ShiftReportQualityProgressBar } from '../components/ShiftReportQualityProgress/ShiftReportQualityProgressBar';
import { ExportOptionsModal } from '../PublishAndOrExportShiftReport/ExportOptionsModal';
import { singleReportExportOptions } from '../PublishAndOrExportShiftReport/exportOptionsUtils';
import { PublishConfirmationModal } from '../PublishAndOrExportShiftReport/PublishConfirmationModal';
import {
  combineShiftReports,
  makeFormValuesFromReportData,
  ShiftReportForm,
  type ShiftReportFormComponentProps,
  useShiftReportFormContext,
} from '../ShiftReportForm/ShiftReportForm';
import { ShiftReportFormLayout } from '../ShiftReportForm/ShiftReportFormLayout';
import { ShiftReportFormLayoutV2 } from '../ShiftReportForm/ShiftReportFormLayoutV2';
import { usePublishModal } from '../ViewShiftReport/hooks/usePublishModal';

const EditShiftReportPrivate = ({ reset, handleSubmit, trigger }: ShiftReportFormComponentProps) => {
  const messages = useMessageGetter('shiftReport.new');
  const approvalMessages = useMessageGetter('shiftReport.approvals');
  const messagesEditReport = useMessageGetter('shiftReport.edit');
  const project = useCurrentProject();
  const teamId = project?.currentTeamId;
  const { data: teamSubscriptionData } = useTeamsSubscriptionPlan(project.id, teamId!);
  const shiftReport = useCurrentShiftReport();
  const { data: reportQualityIndicators } = useShiftReportsQualityIndicators(project.id, shiftReport.id);
  const { mutate: updateShiftReport } = useUpdateShiftReport();
  const {
    formState: { isDirty },
    submitForm,
  } = useShiftReportFormContext();
  const isUpdatingShiftReport = useIsMutating({ mutationKey: ['update-shift-report-mutation'] });
  const isDraftSaving = isUpdatingShiftReport >= 1;
  const submitFormRef = useRef(submitForm);
  const isDraftSavingRef = useRef(isDraftSaving);
  const { data: previousShiftReport } = usePreviousShiftReport(project.id, project?.currentTeamMemberId!);
  const navigate = useNavigate();
  const location = useLocation();

  const { value: shiftReportApprovalsFlag } = useFeatureFlag('shift-report-approvals');
  const { mutate: reviewShiftReport } = useSubmitForReviewShiftReport();

  const isLargeScreen = useMediaQuery(breakpoints.up('sm'));
  const isExportAvailable = Boolean(teamSubscriptionData?.features?.exportShiftReportsData?.available);
  const displayShiftReportFormLayoutV2 = !isLargeScreen;
  const displayShiftReportFormLayout = !displayShiftReportFormLayoutV2;

  const isAuthor = shiftReport?.availableActions.editRootFields !== false;

  const {
    handleCloseExportOptionsModal,
    handleClosePublishModal,
    handleOpenExportOptionsModal,
    handlePreviewDraft,
    handlePublishAndExport,
    handlePublishAttempt,
    isExportOptionsModalOpen,
    isPublishModalOpen,
    goBackToShiftReportDetails,
    redirectToShiftReports,
  } = usePublishModal({ projectId: project.id, shiftReportId: shiftReport.id, trigger });

  const handlePrefillForm = async () => {
    const newFormValues = makeFormValuesFromReportData(
      { ...previousShiftReport!, reportDate: formatDateISO(now(), project?.timezone) },
      true
    );
    const oldFormValues = makeFormValuesFromReportData({ ...shiftReport! });
    const combinedShiftReports = combineShiftReports(oldFormValues, newFormValues);
    updateShiftReport(
      {
        projectId: project.id,
        shiftReportId: shiftReport.id,
        data: combinedShiftReports,
      },
      {
        onSuccess: (updatedShiftReport) => reset(makeFormValuesFromReportData(updatedShiftReport)),
      }
    );
  };

  const handleBack = () => {
    const canGoBack = location.key !== 'default';
    if (location.state?.duplicate) {
      redirectToShiftReports('drafts');
    } else if (canGoBack) {
      navigate(-1);
    } else if (displayShiftReportFormLayoutV2) {
      goBackToShiftReportDetails();
    } else {
      redirectToShiftReports('drafts');
    }
  };

  const renderPageSaveSection = () =>
    (!isDirty || isDraftSaving) && (
      <div data-cy="shift-report-save-feedback">
        <PageSaveLoading
          isSaving={isDraftSaving}
          savingMessage={messages('draftSaving')}
          savedMessage={messages('draftSaved')}
        />
      </div>
    );

  const renderTitle = () => (
    <div className="flex gap-x-2">
      <h1 className="text-lg font-medium leading-8 text-gray-900 md:text-xl md:font-medium md:leading-7">
        {messagesEditReport('title')}
      </h1>
      {!isLargeScreen && renderPageSaveSection()}
    </div>
  );

  const handleReview = async () => {
    reviewShiftReport(
      {
        projectId: project.id,
        shiftReportId: shiftReport.id,
      },
      {
        onSuccess: () => {
          showSuccessToast({
            message: approvalMessages('successReviewToast'),
          });
        },
      }
    );
  };

  const renderPrimaryButton = () => {
    if (shiftReport.state === 'in_review' && shiftReportApprovalsFlag) {
      return (
        <div className="inline-flex">
          <Tooltip.Root>
            <Tooltip.Trigger asChild>
              <Button
                color="primary"
                variant="contained"
                size="md"
                aria-label={approvalMessages('pendingApproval')}
                leadingIcon={isLargeScreen ? DocumentMagnifyingGlassIcon : undefined}
                disabled
              >
                {approvalMessages('pendingApproval')}
              </Button>
            </Tooltip.Trigger>
            <Tooltip.Content side="bottom" className="z-popover">
              {approvalMessages('inReviewTooltip')}
            </Tooltip.Content>
          </Tooltip.Root>
        </div>
      );
    }

    if (shiftReportApprovalsFlag) {
      return (
        <Button
          color="primary"
          variant="contained"
          size="md"
          aria-label={approvalMessages('submitForApproval')}
          leadingIcon={isLargeScreen ? CheckCircleIcon : undefined}
          onClick={handleSubmit(handleReview)}
        >
          {approvalMessages('submitForApproval')}
        </Button>
      );
    }

    return (
      <Button
        color="primary"
        variant="contained"
        size="md"
        data-cy="publish-shift-report-button"
        aria-label={messages('publish')}
        leadingIcon={isLargeScreen ? CheckIcon : undefined}
        disabled={!shiftReport?.availableActions.publish}
        onClick={handleSubmit(handlePublishAttempt)}
      >
        {messages('publish')}
      </Button>
    );
  };

  const Actions = (
    <div className="flex space-x-3 w-full items-center justify-end">
      {isLargeScreen && renderPageSaveSection()}
      {!!reportQualityIndicators && <ShiftReportQualityProgressBar qualityIndicators={reportQualityIndicators} />}
      <div className="flex space-x-2">
        {displayShiftReportFormLayout && (
          <Button
            color="secondary"
            variant="outlined"
            size="md"
            data-cy="preview-draft-shift-report-button"
            leadingIcon={EyeIcon}
            aria-label={messages('preview')}
            onClick={handlePreviewDraft}
          >
            {messages('preview')}
          </Button>
        )}
        {renderPrimaryButton()}
        {displayShiftReportFormLayoutV2 && isAuthor && (
          <ShiftReportActionDropdown previousShiftReport={previousShiftReport} onOverride={handlePrefillForm} />
        )}
      </div>
    </div>
  );

  useEffect(() => {
    submitFormRef.current = submitForm;
    isDraftSavingRef.current = isDraftSaving;
  }, [submitForm, isDraftSaving]);

  useEffect(() => {
    return () => {
      if (!isDraftSavingRef.current) {
        submitFormRef.current();
      }
    };
  }, []);

  return (
    <Page data-cy="edit-shift-report">
      <Page.Header
        className="bg-white pb-2"
        title={useMessage('shiftReport.edit.title')}
        titleAs={renderTitle}
        backNavigationTitle={useMessage('navigation.backTo', { route: 'shift reports' })}
        hasBackNavigation
        onBackNavigation={handleBack}
        rightSection={Actions}
      />
      <div className="sm:py-1 border-b border-b-gray-200 bg-gray-50 px-4 sm:px-8">
        <ShiftReportActionBar
          previousShiftReport={previousShiftReport}
          onOverride={handlePrefillForm}
          isDraftSaving={isDraftSaving}
        />
      </div>
      <Page.Body className="bg-white">
        <div className="container max-w-7xl">
          {displayShiftReportFormLayout && <ShiftReportFormLayout />}
          {displayShiftReportFormLayoutV2 && <ShiftReportFormLayoutV2 />}
        </div>
      </Page.Body>
      {isPublishModalOpen && (
        <PublishConfirmationModal
          open
          handlePublish={() => handlePublishAndExport()}
          handlePublishAndExport={handleOpenExportOptionsModal}
          onClose={handleClosePublishModal}
        />
      )}
      {isExportOptionsModalOpen && (
        <ExportOptionsModal
          isOpen
          onClose={handleCloseExportOptionsModal}
          handleExportReport={(fileType) => handlePublishAndExport(fileType)}
          options={singleReportExportOptions(!isExportAvailable)}
        />
      )}
    </Page>
  );
};

export const EditShiftReport = () => {
  const project = useCurrentProject();
  const shiftReport = useCurrentShiftReport();
  const { isRefetching } = useShiftReport(project.id, shiftReport.id, {
    query: { refetchOnMount: 'always' },
  });

  if (!isRefetching && !shiftReport.availableActions.edit) {
    return <Navigate to={`/projects/${project.id}/shift-reports/${shiftReport.id}`} />;
  }

  return <ShiftReportForm component={EditShiftReportPrivate} />;
};

export { EditShiftReport as Component };
