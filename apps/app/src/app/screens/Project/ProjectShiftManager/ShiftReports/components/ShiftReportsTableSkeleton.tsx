import React from 'react';
import Skeleton from '@shape-construction/arch-ui/src/Skeleton';

interface ShiftReportsTableSkeletonProps {
  length?: number;
}

const ShiftReportsTableSkeleton: React.FC<ShiftReportsTableSkeletonProps> = ({ length }) => (
  <div
    role="progressbar"
    aria-label="Loading table"
    data-cy="shift-reports-table-loading"
    data-testid="shift-reports-table-loading"
    className="space-y-2"
  >
    <Skeleton className="h-16" />
    {Array.from(Array(length).keys()).map((id) => (
      <Skeleton key={id} className="h-3" />
    ))}
  </div>
);

export { ShiftReportsTableSkeleton };
