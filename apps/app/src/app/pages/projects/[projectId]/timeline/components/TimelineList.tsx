import { useEffect, useRef, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import { ArrowUpIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Page from '@shape-construction/arch-ui/src/Page';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useInterval, useMediaQuery, usePrevious, useScroll, useWindowScroll } from '@shape-construction/hooks';
import { getDifferenceBetweenTwoDates } from '@shape-construction/utils/DateTime';
import { useSuspenseInfiniteQuery } from '@tanstack/react-query';
import { HistoryLimits } from 'app/components/SubscriptionPlanFeatures/HistoryLimits/HistoryLimits';
import { batchEvents, type EventItem } from 'app/lib/utils/batch-events';
import { TimelineEventCard } from 'app/pages/projects/[projectId]/timeline/components/TimelineEventCard';
import { TimelineFilterBar } from 'app/pages/projects/[projectId]/timeline/components/TimelineFilterBar';
import type { EventTypesFilterOptions } from 'app/pages/projects/[projectId]/timeline/constants/TimelineFilterOptions';
import { getProjectEventsInifiteQueryOptions } from 'app/queries/projects/events';
import { Waypoint } from 'react-waypoint';
import { useTimelineFilters } from '../hooks/useTimelineFilters';
import { TimelineEmpty } from './TimelineEmpty';
import { TimelineSkeleton } from './TimelineSkeleton';

const newChipThreshold = 300;
const REFRESH_INTERVAL = 1000 * 30; // 30 seconds

export interface TimelineListProps {
  projectId: ProjectSchema['id'];
}

export type TimelineListComponent = StateFullComponent<
  TimelineListProps,
  typeof TimelineSkeleton,
  typeof TimelineEmpty,
  null
>;

type Filter = { types: EventTypesFilterOptions[]; date: string | null; endDate: string | null };

export const TimelineList: TimelineListComponent = ({ projectId }) => {
  const messageGetter = useMessageGetter('timeline');
  const isSmallScreen = useMediaQuery(breakpoints.down('md'));
  const timelineElementRef = useRef<HTMLDivElement>(null);
  const windowScrollY = useWindowScroll().y;
  const containerScrollY = useScroll(timelineElementRef as React.RefObject<HTMLElement>).y;

  const { typesFilter } = useTimelineFilters();
  const defaultTypeOptionsKeys = (Object.keys(typesFilter) as EventTypesFilterOptions[]).filter(
    (key) => typesFilter[key]?.selected
  );

  const [showNewChip, setShowNewerChip] = useState(false);
  const [localFilters, setLocalFilters] = useState<Filter>({
    date: null,
    endDate: null,
    types: defaultTypeOptionsKeys,
  });

  const { data, fetchNextPage, hasNextPage, fetchPreviousPage, isLoading, isFetchingNextPage } =
    useSuspenseInfiniteQuery({
      staleTime: Number.POSITIVE_INFINITY,
      ...getProjectEventsInifiteQueryOptions(projectId, {
        event_type: localFilters.types || undefined,
        starting_after: localFilters.date || undefined,
        ending_before: localFilters.endDate || undefined,
      }),
      select: (cache) =>
        cache?.pages
          .flatMap(({ events }) => events)
          .reduce((acc, event) => batchEvents(event, acc), [] as EventItem[])
          .sort((a, b) => getDifferenceBetweenTwoDates(b.date, a.date)),
    });

  // Related to https://github.com/TanStack/query/discussions/1410
  const scrollRef = isSmallScreen ? window : timelineElementRef.current;
  const scrollYPosition = isSmallScreen ? windowScrollY : containerScrollY;

  const scrollToTop = () => scrollRef?.scrollTo({ top: 0 });

  const handleSelectFilterOption = (selected: Partial<Filter>) => {
    setLocalFilters((current) => ({ ...current, ...selected }));
  };

  const renderUpdateNotification = () => {
    if (!showNewChip) return null;

    return (
      <Button color="primary" size="xs" variant="contained" onClick={scrollToTop} leadingIcon={ArrowUpIcon}>
        {messageGetter('newActivity')}
      </Button>
    );
  };

  const renderContent = () => {
    if (!data) return null;
    if (data.length === 0) return <TimelineEmpty />;

    return (
      <div role="list" aria-label="timeline">
        {data.map((event) => (
          <TimelineEventCard key={event.id} event={event} projectId={projectId} />
        ))}
      </div>
    );
  };

  const firstEventId = data?.[0]?.id;
  const previousFirstEventId = usePrevious(firstEventId);
  const previousProjectId = usePrevious(projectId);

  useEffect(() => {
    if (projectId !== previousProjectId) return;
    if (firstEventId === previousFirstEventId) return;
    if (scrollYPosition < newChipThreshold) return;

    setShowNewerChip(true);
  }, [firstEventId, previousFirstEventId, projectId, previousProjectId, scrollYPosition]);

  useInterval(fetchPreviousPage, REFRESH_INTERVAL);

  return (
    <>
      <div className="border-b border-b-gray-200 bg-gray-50 px-4 py-1 sm:px-8">
        <TimelineFilterBar onSelectFilterOption={handleSelectFilterOption} />
      </div>
      <HistoryLimits featureName="projectTimeline" />

      <Page.Body ref={timelineElementRef} className="flex h-full flex-col">
        <div className="fixed inset-x-0 md:sticky top-18 md:top-0 z-popover flex justify-center">
          {renderUpdateNotification()}
        </div>

        <div className="container relative mx-auto max-w-4xl grow">
          <Waypoint scrollableAncestor={scrollRef} onEnter={() => setShowNewerChip(false)}>
            <div data-testid="waypoint-top" />
          </Waypoint>
          {renderContent()}
          {hasNextPage && (
            <Waypoint scrollableAncestor={scrollRef} bottomOffset={0} onEnter={() => fetchNextPage()}>
              <div data-testid="waypoint-bottom" />
            </Waypoint>
          )}
          {isLoading || isFetchingNextPage ? <TimelineSkeleton /> : null}
        </div>
      </Page.Body>
    </>
  );
};

TimelineList.Loading = TimelineSkeleton;
TimelineList.Empty = TimelineEmpty;
