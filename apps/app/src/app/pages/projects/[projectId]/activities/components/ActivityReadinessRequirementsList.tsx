import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import DraggableList from '@shape-construction/arch-ui/src/DraggableList';
import { TrashIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import InputCheckbox from '@shape-construction/arch-ui/src/InputCheckbox';
import { useContentEditable, useModal } from '@shape-construction/hooks';
import { DeleteActivityRequirementConfirmationModal } from './DeleteActivityRequirementConfirmationModal';
import { EmptyActivityReadinessRequirementsList } from './EmptyActivityReadinessRequirementsList';
import { useActivityReadinessRequirementsList } from './useActivityReadinessRequirementsList';

export interface ActivityReadinessRequirementsListProps {
  shiftActivity: ShiftActivitySchema;
}

export const ActivityReadinessRequirementsList: React.FC<ActivityReadinessRequirementsListProps> = ({
  shiftActivity,
}) => {
  const {
    open: deleteRequirementConfirmationOpen,
    closeModal: closeDeleteRequirementConfirmation,
    openModal: openDeleteRequirementConfirmation,
  } = useModal(false);
  const [selectedRequirementId, setSelectedRequirementId] = useState<string | null>(null);
  const messages = useMessageGetter('shiftManager.activities.readiness');
  const [displayInput, setDisplayInput] = useState(false);
  const getEditableContentProps = useContentEditable();
  const {
    inputRef,
    lastElementRef,
    form,
    activityRequirements = [],
    hasZeroResults,
    onCreateRequirement,
    onUpdateRequirement,
    onDeleteRequirement,
    onToggleRequirementCompletion,
    onMoveRequirement,
  } = useActivityReadinessRequirementsList();

  const renderList = () => {
    if (!displayInput && hasZeroResults)
      return (
        <EmptyActivityReadinessRequirementsList
          displayCreateButton={shiftActivity.availableActions.edit}
          onCreate={() => {
            setDisplayInput(true);
            setTimeout(() => inputRef.current?.focus(), 0);
          }}
        />
      );

    return (
      <DraggableList.Root onDrop={onMoveRequirement}>
        <ul className="pt-2 px-1 flex-1 flex flex-col gap-2 overflow-y-auto">
          <DraggableList.Items items={activityRequirements}>
            {(requirement) => (
              <DraggableList.Item asChild>
                <li
                  key={requirement.id}
                  className="group flex flex-row gap-2 items-center focus-within:outline-solid outline-indigo-400 rounded-sm hover:bg-neutral-subtlest-hovered"
                >
                  <DraggableList.Handle />
                  <div
                    aria-checked={requirement.completed}
                    className="flex flex-row gap-2 items-center w-full rounded-sm"
                  >
                    <input hidden type="submit" />
                    <InputCheckbox
                      name="checked"
                      defaultChecked={requirement.completed}
                      onChange={(e) => onToggleRequirementCompletion(requirement.id, e.target.checked)}
                    />
                    <span
                      className="bg-transparent w-full h-8 flex flex-row items-center text-sm leading-5 font-medium text-neutral-bold placeholder:text-neutral-subtlest focus:ring-0 border-none focus:outline-hidden group-aria-checked:line-through"
                      {...getEditableContentProps({
                        name: 'title',
                        defaultValue: requirement.title,
                        onSubmit: (value) => {
                          if (value === requirement.title) return;
                          onUpdateRequirement(requirement.id, { title: value as string });
                        },
                      })}
                    >
                      {requirement.title}
                    </span>
                    <button
                      aria-label={messages('actions.deleteRequirement')}
                      type="button"
                      className="p-1"
                      onClick={() => {
                        setSelectedRequirementId(requirement.id);
                        openDeleteRequirementConfirmation();
                      }}
                    >
                      <TrashIcon className="text-icon-danger-bold h-4 w-4" />
                    </button>
                  </div>
                </li>
              </DraggableList.Item>
            )}
          </DraggableList.Items>
          {displayInput && (
            <li className="bg-neutral-white sticky bottom-0 px-1 pb-2 flex flex-row gap-1.5 shrink-0 items-center">
              <form
                onSubmit={onCreateRequirement}
                className="shrink-0 h-8 group px-1 flex flex-row gap-2 items-center w-full focus-within:outline-solid outline-indigo-400 rounded-sm hover:bg-neutral-subtlest-hovered"
              >
                <InputCheckbox disabled name="checked" />
                <input
                  {...form.register('title')}
                  ref={(e) => {
                    // We need this as the useForm uses ref and we also need to use ref to focus the input
                    form.register('title').ref(e);
                    inputRef.current = e;
                  }}
                  name="title"
                  className="p-0 bg-transparent w-full h-8 flex flex-row items-center text-sm leading-5 font-medium text-neutral-bold placeholder:text-neutral-subtlest focus:ring-0 border-none"
                  placeholder={messages('requirements.placeholder')}
                />
                <input hidden type="submit" />
              </form>
            </li>
          )}
          <div ref={lastElementRef} />
        </ul>
      </DraggableList.Root>
    );
  };

  return (
    <div className="flex flex-col max-h-96">
      <div>
        <span className="text-sm leading-5 font-normal text-neutral-subtle">{messages('requirements.title')}</span>
      </div>

      {renderList()}

      {!displayInput && !hasZeroResults && (
        <div className="mt-2 h-8">
          <button
            type="button"
            className="flex flex-row items-center gap-1.5"
            onClick={() => {
              setDisplayInput(true);
              requestAnimationFrame(() => {
                lastElementRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
                inputRef.current?.focus({ preventScroll: true });
              });
            }}
          >
            <PlusIcon className="mx-1 h-4 w-4 text-icon-neutral-subtle" />{' '}
            <span className="text-neutral-subtlest">{messages('actions.newRequirement')}</span>
          </button>
        </div>
      )}

      <DeleteActivityRequirementConfirmationModal
        open={deleteRequirementConfirmationOpen}
        onConfirm={() => {
          onDeleteRequirement(selectedRequirementId!);
          closeDeleteRequirementConfirmation();
        }}
        onClose={closeDeleteRequirementConfirmation}
      />
    </div>
  );
};
