import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import Gallery from '@shape-construction/arch-ui/src/Gallery';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { OverlayWithForm } from '@shape-construction/arch-ui/src/Overlays';
import type { GalleryImage } from '@shape-construction/arch-ui/src/types/GalleryImage';

interface Props {
  images: GalleryImage[];
  onClose: () => void;
  onAttachEvidence: () => void;
  onCompleteWorkflow: (data: any) => void;
  open: boolean;
}

export const EvidenceGalleryModal: React.FC<Props> = ({
  open,
  images,
  onClose,
  onAttachEvidence,
  onCompleteWorkflow,
}) => {
  const messages = useMessageGetter('issue.detail.stateActions.assigned');

  return (
    // @ts-expect-error Property 'formProps' is missing in type '{ children: Element; open: boolean; closeOverlay: () => void; title: any; footer: Element; }' but required in type 'OverlayWithFormProps'.
    <OverlayWithForm
      open={open}
      closeOverlay={onClose}
      title={messages('workflow.complete.confirmTitle')}
      footer={
        <>
          <Button
            color="primary"
            variant="outlined"
            size="md"
            data-cy="workflow-complete-add-more"
            leadingIcon={PlusIcon}
            onClick={onAttachEvidence}
          >
            {messages('workflow.complete.addMoreCTA')}
          </Button>
          <Button
            color="primary"
            variant="contained"
            size="md"
            data-cy="workflow-complete-finish"
            onClick={onCompleteWorkflow}
          >
            {messages('workflow.complete.finishCTA')}
          </Button>
        </>
      }
    >
      <Gallery images={images} />
    </OverlayWithForm>
  );
};
