import React from 'react';
import Button from '@shape-construction/arch-ui/src/Button';
import type { ButtonBaseProps } from '@shape-construction/arch-ui/src/Button/ButtonBase';
import { ChevronLeftIcon, ChevronRightIcon } from '@shape-construction/arch-ui/src/Icons/outline';

type BackButtonProps = Pick<ButtonBaseProps, 'form'> & {
  isFirstStep: boolean;
  onBack: () => void;
};

const BackButton: React.FC<BackButtonProps> = ({ isFirstStep, onBack, ...buttonProps }) => (
  <Button
    color="secondary"
    variant="outlined"
    size="md"
    leadingIcon={ChevronLeftIcon}
    disabled={isFirstStep}
    onClick={onBack}
    {...buttonProps}
  >
    Back
  </Button>
);

type NextButtonProps = Pick<ButtonBaseProps, 'form'> & {
  isFormValid: boolean;
  onNext: () => void;
};

const NextButton: React.FC<NextButtonProps> = ({ isFormValid, onNext, ...buttonProps }) => (
  <Button
    color="primary"
    variant="contained"
    size="md"
    type="submit"
    disabled={!isFormValid}
    onClick={onNext}
    {...buttonProps}
  >
    Next
    <ChevronRightIcon />
  </Button>
);

type DoneButtonProps = Pick<ButtonBaseProps, 'form'> & {
  isFormValid: boolean;
  onDone: () => void;
};

const DoneButton: React.FC<DoneButtonProps> = ({ isFormValid, onDone, ...buttonProps }) => (
  <Button
    color="primary"
    variant="contained"
    size="md"
    type="submit"
    disabled={!isFormValid}
    onClick={onDone}
    {...buttonProps}
  >
    Done
  </Button>
);

type StepsFooterProps = {
  formId?: string;
  /**
   * If the current step is the first step
   */
  isFirstStep: boolean;
  /**
   * If the current step is the last step
   */
  isLastStep: boolean;
  /**
   * Is the form is currently valid
   */
  isFormValid: boolean;
  /**
   * Callback to run when the "back" button is clicked
   */
  onBack: () => void;
  /**
   * Callback to run when the "next" button is clicked
   */
  onNext: () => void;
  /**
   * Callback to run when the "done" button is clicked
   */
  onDone: () => void;
};

const StepsFooter: React.FC<StepsFooterProps> = ({
  formId,
  isFirstStep,
  isLastStep,
  isFormValid,
  onBack,
  onNext,
  onDone,
}) => (
  <>
    <BackButton form={formId} isFirstStep={isFirstStep} onBack={onBack} />
    {!isLastStep && <NextButton form={formId} isFormValid={isFormValid} onNext={onNext} />}
    {isLastStep && <DoneButton form={formId} isFormValid={isFormValid} onDone={onDone} />}
  </>
);

export { StepsFooter };
