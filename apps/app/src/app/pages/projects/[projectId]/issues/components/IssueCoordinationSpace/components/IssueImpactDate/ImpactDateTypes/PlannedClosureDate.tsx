import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import { now, parseDateInTimezone } from '@shape-construction/utils/DateTime';
import { IssueImpactDate } from '../IssueImpactDate';
import type { ValidationResponse } from '../types';

export interface PlannedClosureDateProps {
  issue: IssueSchema;
  timezone: string;
  handleSave: (values: any) => void;
}

export const PlannedClosureDate: React.FC<PlannedClosureDateProps> = ({ issue, timezone, handleSave }) => {
  const messages = useMessageGetter('issue.detail.impact.dates');

  const validate = (values: any) => {
    const impactDate = parseDateInTimezone(`${values.date} ${values.time}`, timezone);
    const isImpactDateInThePast = now().isAfter(impactDate);
    const response: ValidationResponse = {};

    if (isImpactDateInThePast) {
      response.warning = messages('shouldBeFuture');
    }

    return response;
  };

  return (
    <IssueImpactDate
      impactFieldName="plannedClosureDate"
      issue={issue}
      timezone={timezone}
      handleSave={handleSave}
      validate={validate}
    />
  );
};
