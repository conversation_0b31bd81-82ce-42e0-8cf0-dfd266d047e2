import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import RadioGroup from '@shape-construction/arch-ui/src/RadioGroup';
import { getImpactRecord, type ImpactType, ImpactValues } from 'app/constants/Impact';
import { Field, useFormikContext } from 'formik';
import { ImpactIcon } from '../ImpactIcon';
import type { ScreenProps, ValidationResponse } from '../index.types';

type ImpactProps = {
  onChangeImpact: (newImpactValue: ImpactType) => void;
};

const Impact: React.FC<ImpactProps> = ({ onChangeImpact }) => {
  const { setFieldValue } = useFormikContext();

  const messages = useMessageGetter('issue.detail');
  const impactRecord = getImpactRecord(messages);

  const handleChangeImpact = (key: string, value: ImpactType) => {
    setFieldValue(key, value);
    onChangeImpact(value);
  };

  const options = ImpactValues.map((status) => ({
    ...impactRecord[status],
    label: (
      <div className="flex gap-x-2">
        <ImpactIcon status={impactRecord[status].value} statusRecord={impactRecord} />
        {impactRecord[status].label}
      </div>
    ),
  }));

  return (
    <Field name="impact">
      {({ field }: any) => (
        <RadioGroup.Root
          variant="card"
          name={field.name}
          value={field.value}
          error={field.error}
          disabled={field.disabled}
          onValueChange={(newValue: ImpactType) => {
            handleChangeImpact(field.name, newValue);
          }}
        >
          <RadioGroup.Items>
            {options.map((item) => (
              <RadioGroup.Item key={item.value} id={item.value} value={item.value}>
                <RadioGroup.Label htmlFor={item.value} description={item.description}>
                  {item.label}
                </RadioGroup.Label>
              </RadioGroup.Item>
            ))}
          </RadioGroup.Items>
        </RadioGroup.Root>
      )}
    </Field>
  );
};

type ImpactScreenProps = {
  messages: any;
  onChangeImpact: (newImpactValue: ImpactType) => void;
};

const addImpactScreen = ({ messages, onChangeImpact }: ImpactScreenProps): ScreenProps => ({
  title: messages.issue.detail.impact.formTitles.addImpact(),
  screen: <Impact onChangeImpact={onChangeImpact} />,
  validate: (): ValidationResponse => ({}),
});

const editImpactScreen = ({ messages, onChangeImpact }: ImpactScreenProps): ScreenProps => ({
  title: messages.issue.detail.impact.formTitles.editImpact(),
  screen: <Impact onChangeImpact={onChangeImpact} />,
  validate: (): ValidationResponse => ({}),
});

export { addImpactScreen, editImpactScreen };
