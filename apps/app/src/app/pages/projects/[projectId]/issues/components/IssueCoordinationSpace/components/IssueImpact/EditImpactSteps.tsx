import React, { useEffect, useState } from 'react';
import { useMessage } from '@messageformat/react';
import Alert from '@shape-construction/arch-ui/src/Alert';
import { OverlayWithForm } from '@shape-construction/arch-ui/src/Overlays';
import { now, roundDownDateToNearestHour } from '@shape-construction/utils/DateTime';
import { joinDateTimeFields, splitDateTimeFields } from 'app/components/UI/DateTime/DateTime';
import type { ImpactField, ImpactType } from 'app/constants/Impact';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useUpdateIssueImpact } from 'app/queries/issues/issues';
import { useFormikContext } from 'formik';
import * as Yup from 'yup';
import { getDefaultDelayStart } from './impact.utils';
import type { FormValues, PartialIssueImpactProps, ScreenProps } from './index.types';
import { delayFinishScreen, delayStartScreen, dueDateScreen, editImpactScreen, workAffectedScreen } from './Steps';
import { StepsFooter } from './StepsFooter';
import { useImpactSteps } from './useImpactSteps';

export interface EditImpactStepsProps {
  isOpen: boolean;
  onCloseImpactSteps: () => void;
  issue: PartialIssueImpactProps;
}

const EditImpactSteps: React.FC<EditImpactStepsProps> = ({ isOpen, onCloseImpactSteps, issue }) => {
  const {
    advanceStep,
    allStepNames,
    currentStepName,
    getStepNames,
    isFirstStep,
    isLastStep,
    resetStep,
    rollbackStep,
    setCurrentImpact,
  } = useImpactSteps({
    issueImpact: issue.impact as ImpactType | undefined,
  });
  const messages = useMessage('');
  const project = useCurrentProject();
  const { mutate: updateIssueImpact } = useUpdateIssueImpact();

  type AlertMessageProps = {
    [key in ImpactField]?: string;
  };

  const [warningMessage, setWarningMessage] = useState<AlertMessageProps>({});
  const [errorMessage, setErrorMessage] = useState<AlertMessageProps>({});

  /**
   * Resets the steps to be the first step (impact selection).
   * Should trigger when the dialog opens.
   */
  useEffect(() => {
    if (isOpen) {
      resetStep();
      resetAlertMessages();
    }
  }, [isOpen, resetStep]);

  const resetAlertMessages = () => {
    setWarningMessage({});
    setErrorMessage({});
  };

  const onChangeImpact = (newImpactValue: ImpactType) => {
    setCurrentImpact(newImpactValue);
  };

  type ScreensProps = {
    [key in ImpactField]: ScreenProps;
  };

  const screens: ScreensProps = {
    impact: editImpactScreen({ messages, onChangeImpact }),
    workAffected: workAffectedScreen({ messages }),
    dueDate: dueDateScreen({ messages, timeZone: project.timezone }),
    delayStart: delayStartScreen({ messages, timeZone: project.timezone }),
    delayFinish: delayFinishScreen({ messages, timeZone: project.timezone }),
  };

  const getStepValue = (fieldName: ImpactField, values: FormValues) => {
    switch (fieldName) {
      case 'dueDate':
      case 'delayStart':
      case 'delayFinish':
        return joinDateTimeFields(fieldName, values, project.timezone);
      case 'impact':
        return { impact: values.impact };
      default:
        return { [fieldName]: values[fieldName] };
    }
  };

  /**
   * This function returns am object of <name, value> attributes ready to update the issue. Example:
   *   {
   *     delayFinish: null,
   *     delayStart: null,
   *     dueDate: "2015-02-21T10:19:00Z",
   *     impact: "noEffect",
   *     workAffected: null,
   *   }
   * - It takes into consideration the current impact, sets the relevant fields with the data from
   *   the values prop and sets as null the other. For example, if the current impact is 'noEffect',
   *   then "impact" and "dueDate" are set from the values prop but "workAffected",
   *   "delayStart", "delayFinish" are set to null.
   * - it joins split date fields. For example, if it needs the "dueDate" attribute, it joins
   *   "dueDateDate" and "dueDateTime" into "dueDate"
   *
   * @param values the current object of formik form values
   */
  const getUpdateAttributes = (values: FormValues) => {
    const currentStepNames = getStepNames();
    return allStepNames.reduce((attrs, stepName) => {
      // Field is shown is the stepper, update value.
      if (currentStepNames.includes(stepName)) {
        return { ...attrs, ...getStepValue(stepName, values) };
      }
      // If the due date wasn’t shown in stepper, do not touch it.
      if (stepName === 'dueDate') {
        return attrs;
      }
      // Any other field not included in the stepper should be cleared.
      return { ...attrs, [stepName]: null };
    }, {});
  };

  const handleValidation = (values: FormValues) => {
    if (currentStepName !== 'impact' && currentStepName !== 'workAffected') {
      const dateValue = values[`${currentStepName}Date`];
      const timeValue = values[`${currentStepName}Time`];

      if (!dateValue || !timeValue) {
        return {
          [`${currentStepName}Date`]: !dateValue && messages.errors.requiredField(),
          [`${currentStepName}Time`]: !timeValue && messages.errors.requiredField(),
        };
      }
    }

    const { messages: alertMessages, errors = {} } = screens[currentStepName].validate(values);

    setWarningMessage((state) => ({ ...state, [currentStepName]: alertMessages?.warning }));
    setErrorMessage((state) => ({ ...state, [currentStepName]: alertMessages?.error }));

    return errors;
  };

  const validationSchema = Yup.object().shape({
    impact: Yup.string(),
    workAffected: Yup.string(),
    dueDateTime: Yup.string().required(messages.errors.requiredField()),
    dueDateDate: Yup.date().required(messages.errors.requiredField()),
    delayStartTime: Yup.string().required(messages.errors.requiredField()),
    delayStartDate: Yup.date().required(messages.errors.requiredField()),
    delayFinishTime: Yup.string().required(messages.errors.requiredField()),
    delayFinishDate: Yup.date().required(messages.errors.requiredField()),
  });

  const formProps = {
    initialValues: {
      impact: issue.impact || '',
      workAffected: issue.workAffected || '',
      ...splitDateTimeFields(
        'dueDate',
        issue.dueDate || roundDownDateToNearestHour(now()).toString(),
        project.timezone
      ),
      ...splitDateTimeFields(
        'delayStart',
        issue.delayStart || getDefaultDelayStart(issue.dueDate, project.timezone),
        project.timezone
      ),
      ...splitDateTimeFields(
        'delayFinish',
        issue.delayFinish || roundDownDateToNearestHour(now()).toString(),
        project.timezone
      ),
    },
    validationSchema,
    validate: handleValidation,
    onSubmit: (values: FormValues) => {
      if (isLastStep()) {
        updateIssueImpact({
          projectId: issue.projectId,
          issueId: issue.id,
          data: {
            issue: getUpdateAttributes(values),
          },
        });
        onCloseImpactSteps();
      } else {
        advanceStep();
      }
    },
  };

  const overlayFormFooter = (
    <OverlayFormFooter
      isFirstStep={isFirstStep()}
      isLastStep={isLastStep()}
      rollbackStep={rollbackStep}
      resetAlertMessages={resetAlertMessages}
    />
  );

  return (
    <OverlayWithForm
      title={screens[currentStepName].title}
      open={isOpen}
      closeOverlay={onCloseImpactSteps}
      noPadding
      formProps={formProps}
      footer={overlayFormFooter}
    >
      <div className="px-4 py-6 md:px-6">
        <div className="space-y-4">
          {warningMessage[currentStepName] && (
            <Alert color="warning" justifyContent="start">
              <Alert.Message>{warningMessage[currentStepName]}</Alert.Message>
            </Alert>
          )}
          {errorMessage[currentStepName] && (
            <Alert color="danger" justifyContent="start">
              <Alert.Message>{errorMessage[currentStepName]}</Alert.Message>
            </Alert>
          )}
          {screens[currentStepName].screen}
        </div>
      </div>
    </OverlayWithForm>
  );
};

type OverlayFormFooterProps = {
  isFirstStep: boolean;
  isLastStep: boolean;
  resetAlertMessages: () => void;
  rollbackStep: () => void;
};

const OverlayFormFooter: React.FC<OverlayFormFooterProps> = ({
  isFirstStep,
  isLastStep,
  rollbackStep,
  resetAlertMessages,
}) => {
  const { setErrors, isValid } = useFormikContext();

  return (
    <StepsFooter
      isFirstStep={isFirstStep}
      isLastStep={isLastStep}
      isFormValid={isValid}
      onBack={() => {
        resetAlertMessages();
        setErrors({});
        rollbackStep();
      }}
      onNext={() => {
        resetAlertMessages();
      }}
      onDone={() => {
        resetAlertMessages();
      }}
    />
  );
};

export { EditImpactSteps };
