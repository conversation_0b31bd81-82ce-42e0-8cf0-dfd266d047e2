import React, { useEffect } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema, ProjectSchema, TeamMemberSchema, UserSchema } from '@shape-construction/api/src/types';
import ActionPanel from '@shape-construction/arch-ui/src/ActionPanel';
import Button from '@shape-construction/arch-ui/src/Button';
import { getImpactRecord } from 'app/constants/Impact';
import { useApprovers } from 'app/hooks/useApprovers';
import { ApproveResolveWorkflow } from '../StateWorkflows/ApproveResolveWorkflow';
import { MarkCompletedWorkflow } from '../StateWorkflows/MarkCompletedWorkflow';
import { useIssueActionWorkflow } from './useIssueActionWorkflow';

export interface InProgressProps {
  issue: IssueSchema;
  timezone: ProjectSchema['timezone'];
  currentUser: UserSchema;
  currentProjectPerson?: TeamMemberSchema;
}

export const InProgress: React.FC<InProgressProps> = ({ issue, currentUser, currentProjectPerson, timezone }) => {
  const messages = useMessageGetter('issue.detail');
  const impactRecord = getImpactRecord(messages);
  const {
    showResolvedWorkflow,
    setShowResolvedWorkflow,
    showCompletedWorkflow,
    setShowCompletedWorkflow,
    setIssueResolved,
    setIssueOutstanding,
    setIssueCompleted,
  } = useIssueActionWorkflow(issue.projectId, issue.id);

  const { assignedUser, approvers, observerUser } = issue;
  const { isApprover, approverPosition } = useApprovers(currentProjectPerson?.id, approvers);

  const isCurrentUserResponsible = assignedUser?.id === currentUser.id;
  const isCurrentUserObserver = observerUser.id === currentUser.id;

  useEffect(() => {
    if (showResolvedWorkflow && !isCurrentUserObserver) {
      setIssueResolved();
    }
  }, [showResolvedWorkflow, isCurrentUserObserver, setIssueResolved]);

  if (isCurrentUserObserver || isApprover || isCurrentUserResponsible) {
    const content = (
      <>
        {messages('stateActions.inProgress.action')}
        {isApprover &&
          !isCurrentUserObserver &&
          ` ${messages('stateActions.inProgress.approverPosition', {
            position: approverPosition,
            length: approvers.length,
          })}`}
      </>
    );

    return (
      <>
        <ActionPanel
          content={content}
          actions={
            <>
              <Button color="secondary" variant="outlined" size="sm" onClick={setIssueOutstanding}>
                {messages('stateActions.inProgress.workflow.outstandingCTA')}
              </Button>
              {isApprover ? (
                <Button
                  color="success"
                  variant="contained"
                  size="sm"
                  onClick={() => setShowResolvedWorkflow(true)}
                  data-cy="approve-issue"
                >
                  {messages('stateActions.inProgress.workflow.approveCTA')}
                </Button>
              ) : (
                <Button color="success" variant="contained" size="sm" onClick={() => setShowCompletedWorkflow(true)}>
                  {messages('stateActions.inProgress.workflow.completeCTA')}
                </Button>
              )}
            </>
          }
        />
        {showCompletedWorkflow && (
          <MarkCompletedWorkflow
            issueId={issue.id}
            onClose={() => setShowCompletedWorkflow(false)}
            onCompleteWorkflow={setIssueCompleted}
            projectId={issue.projectId}
          />
        )}
        {showResolvedWorkflow && isCurrentUserObserver && (
          <ApproveResolveWorkflow
            finishOnly={issue.impact === impactRecord.liveDelay.value}
            onClose={() => {
              setShowResolvedWorkflow(false);
            }}
            onCompleteWorkflow={setIssueResolved}
            issue={issue}
            timezone={timezone}
          />
        )}
      </>
    );
  }

  return null;
};
