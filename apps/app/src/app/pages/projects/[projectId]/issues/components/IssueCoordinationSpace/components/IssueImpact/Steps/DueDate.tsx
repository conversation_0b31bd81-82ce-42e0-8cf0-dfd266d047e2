import React from 'react';
import { now, parseDateInTimezone } from '@shape-construction/utils/DateTime';
import { AlertTimeZone } from 'app/components/UI/AlertTimeZone/AlertTimeZone';
import { DateTime, joinDateTimeFields } from 'app/components/UI/DateTime/DateTime';
import type { FormValues, ScreenProps, ValidationResponse } from '../index.types';

type ValidateProps = {
  messages: any;
  timeZone: string;
};

const parseValues = (values: FormValues, timeZone: string) => {
  const { dueDate } = joinDateTimeFields('dueDate', values, timeZone);
  const dueDateDateTime = parseDateInTimezone(dueDate, timeZone);

  return { dueDateDateTime };
};

const validate =
  ({ messages, timeZone }: ValidateProps) =>
  (values: FormValues): ValidationResponse => {
    const { dueDateDateTime } = parseValues(values, timeZone);
    const response: ValidationResponse = {};

    if (now().isAfter(dueDateDateTime)) {
      response.messages = {
        warning: messages.issue.detail.impact.dates.shouldBeFuture(),
      };
    }

    return response;
  };

type DueDateProps = {
  timeZone: string;
};

const DueDate: React.FC<DueDateProps> = ({ timeZone }) => (
  <>
    <DateTime fieldPrefix="dueDate" timezone={timeZone} />
    <AlertTimeZone timeZone={timeZone} />
  </>
);

type DueDateScreenProps = {
  messages: any;
  timeZone: string;
};

const dueDateScreen = ({ messages, timeZone }: DueDateScreenProps): ScreenProps => ({
  title: messages.issue.detail.impact.formTitles.dueDate(),
  screen: <DueDate timeZone={timeZone} />,
  validate: validate({ messages, timeZone }),
});

export { dueDateScreen };
