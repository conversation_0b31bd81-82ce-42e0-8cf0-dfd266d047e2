import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import { DetailItem } from '@shape-construction/arch-ui/src/DetailItem';
import { getImpactRecord } from 'app/constants/Impact';
import { EditImpactSteps } from './EditImpactSteps';
import { ImpactIcon } from './ImpactIcon';
import type { PartialIssueImpactProps } from './index.types';

type IssueImpactProps = {
  issue: PartialIssueImpactProps;
};

const IssueImpactPanel: React.FC<IssueImpactProps> = ({ issue }) => {
  const messages = useMessageGetter('issue.detail');
  const impactRecord = getImpactRecord(messages);
  const impact = issue.impact || impactRecord.notEntered.value;
  const [editImpactSteps, setEditImpactSteps] = useState(false);

  if (!issue.id) return null;
  if (!impact) return null;

  const onOpenImpactSteps = () => {
    setEditImpactSteps(true);
  };

  const onCloseImpactSteps = () => {
    setEditImpactSteps(false);
  };

  return (
    <>
      <DetailItem
        disabled={!issue.availableActions.edit}
        expandable={false}
        onClick={onOpenImpactSteps}
        icon={<ImpactIcon status={impact} statusRecord={impactRecord} />}
        title={impactRecord[impact].label}
      />
      <EditImpactSteps isOpen={editImpactSteps} issue={issue} onCloseImpactSteps={onCloseImpactSteps} />
    </>
  );
};

export { IssueImpactPanel };
