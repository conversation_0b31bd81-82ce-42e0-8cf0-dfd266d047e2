import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Alert from '@shape-construction/arch-ui/src/Alert';
import Button from '@shape-construction/arch-ui/src/Button';
import { OverlayWithForm } from '@shape-construction/arch-ui/src/Overlays';
import {
  formatDateAndTimeToUtc,
  formatDateISO,
  formatTime,
  now,
  setEndOfBusiness,
} from '@shape-construction/utils/DateTime';
import { AlertTimeZone } from 'app/components/UI/AlertTimeZone/AlertTimeZone';
import { DateTime } from 'app/components/UI/DateTime/DateTime';

interface FormValues {
  plannedClosureDate: string;
  plannedClosureTime: string;
}

interface FormProps {
  initialValues: FormValues;
  onSubmit: (values: any) => any;
  validate: (values: any) => any;
}

interface Props {
  open: boolean;
  onClose: () => void;
  onSubmit: (values: FormValues) => void;
  onSkip: () => void;
  timezone: string;
}

export const PlannedClosureDateModal: React.FC<Props> = ({ open, onClose, onSubmit, onSkip, timezone }) => {
  const [formWarning, setFormWarning] = useState<string>('');

  const messages = useMessageGetter('issue.detail.stateActions.assignmentRequested');
  const dateTimeMessages = useMessageGetter('dateTime');
  const currentDateTime = now();

  const validate = (values: FormValues) => {
    const plannedClosureDateTime = formatDateAndTimeToUtc(
      `${values.plannedClosureDate} ${values.plannedClosureTime}`,
      timezone
    );

    setFormWarning(now().isAfter(plannedClosureDateTime) ? dateTimeMessages('dateShouldBeFuture') : '');

    return {};
  };

  const plannedClosureTime = now().isAfter(setEndOfBusiness(currentDateTime))
    ? currentDateTime.add(1, 'hour')
    : setEndOfBusiness(currentDateTime);

  const formProps: FormProps = {
    initialValues: {
      plannedClosureDate: formatDateISO(currentDateTime, timezone),
      plannedClosureTime: formatTime(plannedClosureTime, timezone),
    },
    onSubmit,
    validate,
  };

  return (
    <OverlayWithForm
      open={open}
      closeOverlay={onClose}
      title={messages('workflow.approve.plannedClosureDate.title')}
      formProps={formProps}
      footer={
        <>
          <Button color="primary" variant="contained" size="md" type="submit">
            {messages('workflow.approve.plannedClosureDate.submitCTA')}
          </Button>
          <Button color="secondary" variant="outlined" size="md" onClick={onSkip}>
            {messages('workflow.approve.plannedClosureDate.skipCTA')}
          </Button>
        </>
      }
    >
      <>
        {formWarning && (
          <div className="mb-4">
            <Alert color="warning" justifyContent="start">
              <Alert.Message>{formWarning}</Alert.Message>
            </Alert>
          </div>
        )}
        <DateTime fieldPrefix="plannedClosure" timezone={timezone} />
        <AlertTimeZone timeZone={timezone} />
      </>
    </OverlayWithForm>
  );
};
