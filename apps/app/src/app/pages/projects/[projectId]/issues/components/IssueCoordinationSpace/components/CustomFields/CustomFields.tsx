import React, { useMemo, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueCustomFieldListSchema, IssueSchema } from '@shape-construction/api/src/types';
import { ProjectWideCustomFieldIcon } from 'app/components/UI/Icons/ProjectWideCustomFieldIcon';
import { TeamCustomFieldIcon } from 'app/components/UI/Icons/TeamCustomFieldIcon';
import { useUpdateCustomFields } from 'app/queries/issues/customFields/custom.fields';
import type { FormikValues } from 'formik';
import { CustomFieldEditModal } from './CustomFieldsEditModal';
import { CustomFieldsSection } from './CustomFieldsSection';

interface CustomFieldsProps {
  issue: IssueSchema;
}

export const CustomFields: React.FC<CustomFieldsProps> = ({ issue }) => {
  const messages = useMessageGetter('issue.detail.customFields');

  const [selectedCustomFields, setSelectedCustomFields] = useState<IssueCustomFieldListSchema | null>(null);

  const closeOverlay = () => setSelectedCustomFields(null);

  const onEdit = (customFields: IssueCustomFieldListSchema) =>
    issue.availableActions.edit ? setSelectedCustomFields(customFields) : null;

  const { mutate: updateCustomFields } = useUpdateCustomFields();
  const handleSave = (values: FormikValues) => {
    updateCustomFields({
      projectId: issue.projectId,
      issueId: issue.id,
      data: Object.entries(values).map(([customFieldId, value]) => ({
        custom_field_id: customFieldId,
        value,
      })),
    });
  };

  const customFields = useMemo(() => issue.customFields ?? [], [issue.customFields]);

  const { projectWide, team } = useMemo(
    () => ({
      projectWide: customFields.filter((field) => field.projectWide === true),
      team: customFields.filter((field) => field.projectWide === false),
    }),
    [customFields]
  );

  if (customFields.length === 0) return null;

  return (
    <section className="flex flex-col space-y-3" data-cy="custom-fields">
      <h2 className="text-base font-medium leading-6">{messages('title')}</h2>

      {selectedCustomFields ? (
        <CustomFieldEditModal customFields={selectedCustomFields} handleSave={handleSave} onClose={closeOverlay} open />
      ) : null}

      {projectWide.length > 0 ? (
        <CustomFieldsSection
          customFields={projectWide}
          icon={<ProjectWideCustomFieldIcon className="mr-2 h-5 w-5 text-gray-400" />}
          title={messages('projectWide')}
          onEdit={onEdit}
        />
      ) : null}

      {team.length > 0 ? (
        <CustomFieldsSection
          customFields={team}
          icon={<TeamCustomFieldIcon className="mr-2 h-5 w-5 text-gray-400" />}
          title={messages('team')}
          onEdit={onEdit}
        />
      ) : null}
    </section>
  );
};
