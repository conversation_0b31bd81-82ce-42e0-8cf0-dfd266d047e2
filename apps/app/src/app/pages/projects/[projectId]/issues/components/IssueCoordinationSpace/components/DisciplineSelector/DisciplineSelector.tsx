import React, { useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { DisciplineListSchema } from '@shape-construction/api/src/types';
import { type Node, TreeSelector } from '@shape-construction/arch-ui/src/TreeSelector/TreeSelector';
import { disciplineLevel0, disciplinesToNodes } from 'app/components/Utils/disciplines';
import { Field } from 'formik';
import messages from 'messages';
import * as Yup from 'yup';

export interface DisciplineSelectorProps {
  disciplines?: DisciplineListSchema;
}

export const DisciplineSelector: React.FC<DisciplineSelectorProps> = ({ disciplines = [] }) => {
  const disciplinesNodes: Node[] = useMemo(() => disciplinesToNodes(disciplines), [disciplines]);
  const disciplinesWithLevel0Parent: Node[] = useMemo(
    () => [
      ...disciplinesNodes.map((disciplineNode: Node) => ({
        ...disciplineNode,
        parentId: disciplineNode.id === disciplineLevel0.id ? null : (disciplineNode.parentId ?? disciplineLevel0.id),
      })),
    ],
    [disciplinesNodes]
  );

  const searchMessages = useMessageGetter('search');
  const treeSelectorMessages = useMessageGetter('treeSelector');

  return (
    <div data-cy="discipline-selector">
      <Field name="disciplineId">
        {({ field, form }: any) => (
          <TreeSelector
            {...field}
            nodes={disciplinesWithLevel0Parent}
            onSelect={form.setFieldValue}
            defaultSelectedNodeId={form.values.disciplineId}
            rootNodeId={disciplinesWithLevel0Parent[0].id}
            searchFieldPlaceholder={searchMessages('disciplines.placeholder')}
            searchNoResultsMessage={searchMessages('noResults.title')}
            searchNoResultsDescription={searchMessages('noResults.subTitle')}
            clearAllMessage={treeSelectorMessages('clearAll')}
          />
        )}
      </Field>
    </div>
  );
};

/**
 * Function that returns the validation schema for the discipline form
 * @param disciplines list of all disciplines
 */
export const disciplineValidationSchema = () =>
  Yup.object().shape({
    disciplineId: Yup.string().notOneOf(['level0']).required(messages.errors.requiredField()),
  });
