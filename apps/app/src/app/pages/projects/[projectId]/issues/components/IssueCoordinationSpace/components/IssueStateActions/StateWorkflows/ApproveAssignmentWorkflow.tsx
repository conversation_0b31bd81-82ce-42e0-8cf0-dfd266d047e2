import React, { useState } from 'react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import { formatDateAndTimeToUtc } from '@shape-construction/utils/DateTime';
import { useIssueAcceptAssignment } from 'app/queries/issues/assignments/assignments';
import { useUpdateIssue } from 'app/queries/issues/issues';
import { ConfirmModal } from './ApproveAssignmentWorkflow/ConfirmModal';
import { PlannedClosureDateModal } from './ApproveAssignmentWorkflow/PlannedClosureDateModal';

interface FormValues {
  plannedClosureDate: string;
  plannedClosureTime: string;
}

export interface ApproveAssignmentWorkflowProps {
  issue: IssueSchema;
  timezone: string;
  onClose: () => void;
}

export const ApproveAssignmentWorkflow: React.FC<ApproveAssignmentWorkflowProps> = ({ issue, onClose, timezone }) => {
  const { mutate: updateIssue } = useUpdateIssue();
  const { mutate: acceptIssueAssignment } = useIssueAcceptAssignment();
  const [showWorkflow, setShowWorkflow] = useState(false);

  const handleSubmit = (values: FormValues) => {
    updateIssue({
      projectId: issue.projectId,
      issueId: issue.id,
      data: {
        issue: {
          planned_closure_date: formatDateAndTimeToUtc(
            `${values.plannedClosureDate} ${values.plannedClosureTime}`,
            timezone
          ),
        },
      },
    });

    assignAnswerAndClose();
  };

  const assignAnswerAndClose = () => {
    acceptIssueAssignment({
      projectId: issue.projectId,
      issueId: issue.id,
      issueAssignmentId: issue.issueAssignment!.id,
    });
    onClose();
  };

  return (
    <>
      <ConfirmModal open={!showWorkflow} onClose={onClose} onConfirm={() => setShowWorkflow(true)} />
      <PlannedClosureDateModal
        open={showWorkflow}
        onClose={onClose}
        onSubmit={handleSubmit}
        onSkip={assignAnswerAndClose}
        timezone={timezone}
      />
    </>
  );
};
