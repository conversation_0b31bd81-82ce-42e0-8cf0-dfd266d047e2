import React from 'react';
import { MinusCircleIcon as MinusCircleIconOutline } from '@shape-construction/arch-ui/src/Icons/outline';
import { CheckCircleIcon, MinusCircleIcon as MinusCircleIconSolid } from '@shape-construction/arch-ui/src/Icons/solid';
import { textImpactColors } from 'app/colors';
import type { ImpactRecord, ImpactType } from 'app/constants/Impact';

export interface ImpactIconProps {
  status: ImpactType;
  statusRecord: ImpactRecord;
}

const ImpactIcon: React.FC<ImpactIconProps> = ({ status, statusRecord }) => {
  const iconClass = textImpactColors[status];
  if (status === statusRecord.notEntered.value) {
    return <MinusCircleIconOutline className={`h-5 w-5 ${iconClass}`} />;
  }
  if (status === statusRecord.completedDelay.value) {
    return <CheckCircleIcon className={`h-5 w-5 ${iconClass}`} />;
  }
  return <MinusCircleIconSolid className={`h-5 w-5 ${iconClass}`} />;
};

export { ImpactIcon };
