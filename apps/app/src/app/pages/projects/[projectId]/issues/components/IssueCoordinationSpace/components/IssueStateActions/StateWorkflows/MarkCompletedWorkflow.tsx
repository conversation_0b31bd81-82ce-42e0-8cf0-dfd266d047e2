import React, { useRef, useState } from 'react';
import {
  type IssueImageSchema,
  type IssueSchema,
  issueImageKindEnum,
  type ProjectSchema,
} from '@shape-construction/api/src/types';
import type { GalleryImage } from '@shape-construction/arch-ui/src/types/GalleryImage';
import { UploadImageIssueInput } from 'app/components/UI/Camera/UploadImageIssueInput';
import { ConfirmAttachEvidenceModal } from './MarkCompletedWorkflow/ConfirmAttachEvidenceModal';
import { EvidenceGalleryModal } from './MarkCompletedWorkflow/EvidenceGalleryModal';

interface Props {
  issueId: IssueSchema['id'];
  onClose: () => void;
  onCompleteWorkflow: () => void;
  projectId: ProjectSchema['id'];
}

export const MarkCompletedWorkflow: React.FC<Props> = ({ issueId, onClose, onCompleteWorkflow, projectId }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [images, setImages] = useState<GalleryImage[]>([]);

  const addToUploadedImages = (uploadedImageUrls: IssueImageSchema['url'][]) => {
    const lastIndex: number = images.length;

    const newImages: GalleryImage[] = uploadedImageUrls.map((imageUrls, index) => ({
      id: `evidence-image-${lastIndex + index}`,
      imageOriginalUrl: imageUrls.original,
      imageDownloadUrl: imageUrls.download,
      imageLargeUrl: imageUrls.xxl,
      imageMediumUrl: imageUrls.l,
      imageThumbnailUrl: imageUrls.s,
    }));

    setImages([...images, ...newImages]);
  };

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <>
      <ConfirmAttachEvidenceModal
        open={images.length === 0}
        onAttachEvidence={triggerFileInput}
        onSkip={onCompleteWorkflow}
        onClose={onClose}
      />
      <EvidenceGalleryModal
        open={images.length > 0}
        images={images}
        onAttachEvidence={triggerFileInput}
        onCompleteWorkflow={onCompleteWorkflow}
        onClose={onClose}
      />
      <UploadImageIssueInput
        className="hidden"
        kind={issueImageKindEnum.evidence}
        issueId={issueId}
        onUploadedImages={addToUploadedImages}
        projectId={projectId}
        ref={fileInputRef}
      />
    </>
  );
};
