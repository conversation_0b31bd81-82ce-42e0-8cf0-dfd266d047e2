import React, { useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema } from '@shape-construction/api/src/types';
import Alert from '@shape-construction/arch-ui/src/Alert';
import { IssueRestoreConfirmationModal } from './components/IssueRestore/IssueRestoreConfirmationModal';

export interface SectionBannerProps {
  issue: IssueSchema;
}

export const SectionBanner: React.FC<SectionBannerProps> = ({ issue }) => {
  const actionsMessages = useMessageGetter('actions');
  const bannerMessages = useMessageGetter('issue.detail.banners');
  const [isIssueRestoreOpen, setIssueRestoreOpen] = useState(false);

  const renderArchived = () => (
    <>
      {issue.archived && (
        <Alert color="warning">
          <Alert.Message>{bannerMessages('archived')}</Alert.Message>
          {issue.availableActions.edit && (
            <Alert.Actions>
              <button
                type="button"
                className="text-sm font-medium leading-5 underline"
                onClick={() => setIssueRestoreOpen(true)}
              >
                {actionsMessages('restore')}
              </button>
            </Alert.Actions>
          )}
        </Alert>
      )}

      <IssueRestoreConfirmationModal
        projectId={issue.projectId}
        issueId={issue.id}
        open={isIssueRestoreOpen}
        onClose={() => setIssueRestoreOpen(false)}
      />
    </>
  );

  const renderDrafted = () =>
    issue.currentState === 'draft' && <Alert color="warning">{bannerMessages('draft')}</Alert>;

  return (
    <>
      {renderArchived()}
      {renderDrafted()}
    </>
  );
};
