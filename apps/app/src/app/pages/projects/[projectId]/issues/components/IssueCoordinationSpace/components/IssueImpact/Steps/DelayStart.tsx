import React from 'react';
import { now, parseDateInTimezone } from '@shape-construction/utils/DateTime';
import { AlertTimeZone } from 'app/components/UI/AlertTimeZone/AlertTimeZone';
import { DateTime, joinDateTimeFields } from 'app/components/UI/DateTime/DateTime';
import type { FormValues, ScreenProps, ValidationResponse } from '../index.types';

type ValidateProps = {
  messages: any;
  timeZone: string;
};

const parseValues = (values: FormValues, timeZone: string) => {
  const { delayStart } = joinDateTimeFields('delayStart', values, timeZone);
  const delayStartDateTime = parseDateInTimezone(delayStart, timeZone);

  return { delayStartDateTime };
};

const validate =
  ({ messages, timeZone }: ValidateProps) =>
  (values: FormValues): ValidationResponse => {
    const { delayStartDateTime } = parseValues(values, timeZone);
    const response: ValidationResponse = {};

    if (now().isBefore(delayStartDateTime)) {
      response.messages = {
        warning: messages.issue.detail.impact.dates.shouldBePast(),
      };
    }

    return response;
  };

type DelayStartProps = {
  timeZone: string;
};

const DelayStart: React.FC<DelayStartProps> = ({ timeZone }) => (
  <>
    <AlertTimeZone timeZone={timeZone} />
    <DateTime fieldPrefix="delayStart" timezone={timeZone} />
  </>
);

type DelayStartScreenProps = {
  messages: any;
  timeZone: string;
};

const delayStartScreen = ({ messages, timeZone }: DelayStartScreenProps): ScreenProps => ({
  title: messages.issue.detail.impact.formTitles.delayStart(),
  screen: <DelayStart timeZone={timeZone} />,
  validate: validate({ messages, timeZone }),
});

export { delayStartScreen };
