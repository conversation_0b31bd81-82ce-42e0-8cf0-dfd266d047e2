import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Modal from '@shape-construction/arch-ui/src/Modal';
import classNames from 'clsx';
import type { FormikConfig, FormikValues } from 'formik';
import { Form, Formik, type FormikProps } from 'formik';

export interface NewIssueModalProps {
  /**
   * Issue Builder step
   */
  children: React.ReactNode;
  /**
   * Instructs the upper state to update open prop to false
   */
  onClose: (formikProps: FormikProps<FormikValues>) => void;
  /**
   * Custom footer that replaces the default if passed
   */
  footer: React.ReactNode;
  /**
   * Config props passed to Formik
   */
  formProps: FormikConfig<FormikValues>;
  /**
   * Modal open state
   */
  open: boolean;
}

export const NewIssueModal: React.FC<NewIssueModalProps> = ({ children, footer, onClose, formProps, open }) => {
  const messages = useMessageGetter('issue.new');

  return (
    <Formik {...formProps}>
      {(formikProps) => (
        <Modal.Root open={open} onClose={() => onClose(formikProps)}>
          <Modal.Header onClose={() => onClose(formikProps)}>
            <Modal.Title>{messages('title')}</Modal.Title>
          </Modal.Header>
          <Modal.Content className="px-0 h-full md:h-80vh md:max-h-[800px]">
            <Form placeholder="" onPointerEnterCapture={() => {}} onPointerLeaveCapture={() => {}}>
              <div className={classNames('flex flex-col gap-y-3')} data-testid="field-wrapper">
                {children}
              </div>
            </Form>
          </Modal.Content>
          <Modal.Footer>{footer}</Modal.Footer>
        </Modal.Root>
      )}
    </Formik>
  );
};
