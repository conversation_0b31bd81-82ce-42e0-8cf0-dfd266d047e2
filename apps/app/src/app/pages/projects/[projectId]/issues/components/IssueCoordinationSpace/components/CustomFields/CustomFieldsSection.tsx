import React, { type ReactNode } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueCustomFieldListSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import { DetailItem } from '@shape-construction/arch-ui/src/DetailItem';
import ExpansionPanel from '@shape-construction/arch-ui/src/ExpansionPanel';
import { PencilIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { IssueInfo } from '../IssueInfo/IssueInfo';

interface CustomFieldsSectionProps {
  customFields: IssueCustomFieldListSchema;
  icon: ReactNode;
  title: string;
  onEdit: (customFields: IssueCustomFieldListSchema) => void;
}

export const CustomFieldsSection: React.FC<CustomFieldsSectionProps> = ({ customFields, icon, title, onEdit }) => {
  const messages = useMessageGetter('issue.detail.customFields');

  return (
    <section>
      <ExpansionPanel defaultOpen>
        <ExpansionPanel.Header>
          {icon}
          {title}
        </ExpansionPanel.Header>
        <ExpansionPanel.Content>
          {customFields.map((customField) => (
            <DetailItem expandable={false} key={customField.customFieldId} onClick={() => onEdit([customField])}>
              <IssueInfo title={customField.label} content={customField.value || messages('noData')} />
            </DetailItem>
          ))}
          {customFields.length >= 1 ? (
            <div className="mt-1 ml-7 border-t border-gray-200 pt-2">
              <Button
                color="primary"
                variant="text"
                size="md"
                leadingIcon={PencilIcon}
                onClick={() => onEdit(customFields)}
              >
                {messages('editAll')}
              </Button>
            </div>
          ) : null}
        </ExpansionPanel.Content>
      </ExpansionPanel>
    </section>
  );
};
