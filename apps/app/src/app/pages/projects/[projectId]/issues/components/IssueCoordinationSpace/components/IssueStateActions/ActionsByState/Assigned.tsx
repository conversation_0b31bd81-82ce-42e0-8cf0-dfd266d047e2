import React, { useEffect } from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { IssueSchema, ProjectSchema, TeamMemberSchema, UserSchema } from '@shape-construction/api/src/types';
import ActionPanel from '@shape-construction/arch-ui/src/ActionPanel';
import Button from '@shape-construction/arch-ui/src/Button';
import { PersonItemInline } from 'app/components/PersonItem/PersonItemInline';
import { getImpactRecord } from 'app/constants/Impact';
import { useApprovers } from 'app/hooks/useApprovers';
import { ApproveResolveWorkflow } from '../StateWorkflows/ApproveResolveWorkflow';
import { MarkCompletedWorkflow } from '../StateWorkflows/MarkCompletedWorkflow';
import { useIssueActionWorkflow } from './useIssueActionWorkflow';

export interface AssignedProps {
  issue: IssueSchema;
  timezone: ProjectSchema['timezone'];
  currentUser: UserSchema;
  currentProjectPerson?: TeamMemberSchema;
}

export const Assigned: React.FC<AssignedProps> = ({ issue, currentUser, currentProjectPerson, timezone }) => {
  const messages = useMessageGetter('issue.detail');
  const impactRecord = getImpactRecord(messages);
  const {
    showResolvedWorkflow,
    setShowResolvedWorkflow,
    showCompletedWorkflow,
    setShowCompletedWorkflow,
    setIssueInProgress,
    setIssueCompleted,
    setIssueResolved,
  } = useIssueActionWorkflow(issue.projectId, issue.id);
  const { assignedUser, approvers, observerUser } = issue;
  const { isApprover, isFinalApprover, approverPosition } = useApprovers(currentProjectPerson?.id, approvers);

  const isCurrentUserResponsible = assignedUser?.id === currentUser.id;
  const isCurrentUserObserver = observerUser.id === currentUser.id;

  useEffect(() => {
    if (showResolvedWorkflow && !isCurrentUserObserver) {
      setIssueResolved();
    }
  }, [showResolvedWorkflow, isCurrentUserObserver, setIssueResolved]);

  if ((isCurrentUserResponsible || isApprover) && assignedUser) {
    const content = (
      <>
        {`${messages('stateActions.assigned.introAction')} `}
        {isCurrentUserResponsible ? (
          `${messages('stateActions.assigned.meResponsible')} `
        ) : (
          <>
            <PersonItemInline user={assignedUser} />
            &nbsp;
          </>
        )}
        {`${messages('stateActions.assigned.endAction')}`}
        {isApprover && !isCurrentUserObserver
          ? ` ${messages('stateActions.assigned.approverPosition', {
              position: approverPosition,
              length: approvers.length,
            })}`
          : null}
      </>
    );

    const approveLabel = isFinalApprover
      ? messages('stateActions.assigned.workflow.resolveCTA')
      : messages('stateActions.assigned.workflow.approveCTA');

    return (
      <>
        <ActionPanel
          content={content}
          actions={
            <>
              <Button color="primary" variant="contained" size="sm" onClick={setIssueInProgress}>
                {messages('stateActions.assigned.workflow.inProgressCTA')}
              </Button>
              {isApprover ? (
                <Button
                  color="success"
                  variant="contained"
                  size="sm"
                  onClick={() => setShowResolvedWorkflow(true)}
                  data-cy="approve-issue"
                >
                  {approveLabel}
                </Button>
              ) : (
                <Button color="success" variant="contained" size="sm" onClick={() => setShowCompletedWorkflow(true)}>
                  {messages('stateActions.assigned.workflow.completeCTA')}
                </Button>
              )}
            </>
          }
        />
        {showCompletedWorkflow && (
          <MarkCompletedWorkflow
            issueId={issue.id}
            onClose={() => setShowCompletedWorkflow(false)}
            onCompleteWorkflow={setIssueCompleted}
            projectId={issue.projectId}
          />
        )}
        {showResolvedWorkflow && isCurrentUserObserver && (
          <ApproveResolveWorkflow
            finishOnly={issue.impact === impactRecord.liveDelay.value}
            onClose={() => setShowResolvedWorkflow(false)}
            onCompleteWorkflow={setIssueResolved}
            issue={issue}
            timezone={timezone}
          />
        )}
      </>
    );
  }

  return null;
};
