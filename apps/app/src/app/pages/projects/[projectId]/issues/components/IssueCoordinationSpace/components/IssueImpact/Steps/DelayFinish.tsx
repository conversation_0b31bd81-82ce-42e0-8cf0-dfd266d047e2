import React from 'react';
import { now, parseDateInTimezone } from '@shape-construction/utils/DateTime';
import { AlertTimeZone } from 'app/components/UI/AlertTimeZone/AlertTimeZone';
import { DateTime, joinDateTimeFields } from 'app/components/UI/DateTime/DateTime';
import type { FormValues, ScreenProps, ValidationResponse } from '../index.types';

type ValidateProps = {
  messages: any;
  timeZone: string;
};

const parseValues = (values: any, timeZone: string) => {
  const { delayStart } = joinDateTimeFields('delayStart', values, timeZone);
  const delayStartDateTime = parseDateInTimezone(delayStart, timeZone);
  const { delayFinish } = joinDateTimeFields('delayFinish', values, timeZone);
  const delayFinishDateTime = parseDateInTimezone(delayFinish, timeZone);

  return { delayStartDateTime, delayFinishDateTime };
};

const validate =
  ({ messages, timeZone }: ValidateProps) =>
  (values: FormValues): ValidationResponse => {
    const { delayStartDateTime, delayFinishDateTime } = parseValues(values, timeZone);
    const response: ValidationResponse = {};

    if (delayFinishDateTime.isBefore(delayStartDateTime)) {
      response.messages = { error: messages.issue.detail.impact.dates.afterDelayStart() };
      response.errors = { delayFinishDate: true, delayFinishTime: true };
    } else if (now().isBefore(delayFinishDateTime)) {
      response.messages = { warning: messages.issue.detail.impact.dates.shouldBePast() };
    }

    return response;
  };

type DelayFinishProps = {
  timeZone: string;
};

const DelayFinish: React.FC<DelayFinishProps> = ({ timeZone }) => (
  <>
    <AlertTimeZone timeZone={timeZone} />
    <DateTime fieldPrefix="delayFinish" timezone={timeZone} />
  </>
);

type DelayFinishScreenProps = {
  messages: any;
  timeZone: string;
};

const delayFinishScreen = ({ messages, timeZone }: DelayFinishScreenProps): ScreenProps => ({
  title: messages.issue.detail.impact.formTitles.delayFinish(),
  screen: <DelayFinish timeZone={timeZone} />,
  validate: validate({ messages, timeZone }),
});

export { delayFinishScreen };
