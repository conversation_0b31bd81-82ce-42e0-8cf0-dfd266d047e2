import React, { forwardRef, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { Calendar, type DayPickerSingleProps } from '@shape-construction/arch-ui/src/DatePickerCalendar';
import { ExclamationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import type { SelectRootProps } from '@shape-construction/arch-ui/src/Select';
import * as Select from '@shape-construction/arch-ui/src/Select';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useClickAway, useMediaQuery } from '@shape-construction/hooks';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';

type DatePickerProps = SelectRootProps<Date | string | undefined, false> & {
  placeholder?: string;
  disabledDates?: DayPickerSingleProps['disabled'];
  prefilledDate?: Date;
  showError?: boolean;
};

export const DatePicker = forwardRef<React.ElementRef<typeof Select.Root>, DatePickerProps>(
  ({ onChange, value, placeholder, disabledDates, prefilledDate, showError, ...props }, ref) => {
    const isSmallScreen = useMediaQuery(breakpoints.down('md'));
    const [panelOpen, setPanelOpen] = useState(false);
    const [selectedDate, setSelectedDate] = useState<Date | undefined>(value ? new Date(value) : undefined);
    const responsivePanelRef = useRef(null);
    const selectedValue = selectedDate || prefilledDate;

    useClickAway(responsivePanelRef, () => closePanel());

    useEffect(() => {
      setSelectedDate(value ? new Date(value) : undefined);
    }, [value]);

    const openPanel = () => setPanelOpen(true);
    const closePanel = () => setPanelOpen(false);

    const onSelect: DayPickerSingleProps['onSelect'] = (day) => {
      if (day) onChange?.(parseDateWithFormat(day, 'YYYY-MM-DD'));
      setSelectedDate(day);
      closePanel();
    };

    const panelWrapper = isSmallScreen ? (children: React.ReactNode, _: Element) => children : createPortal;

    const errorClassname = showError ? 'border-red-400 focus:ring-red-400 focus:border-red-400' : '';

    return (
      <Select.Root {...props} ref={ref}>
        <Select.Trigger onClick={openPanel} size="md" className={errorClassname}>
          <div className="flex justify-between">
            {selectedValue ? parseDateWithFormat(selectedValue, 'DD-MMM-YYYY') : placeholder}
            {showError && <ExclamationCircleIcon className="h-5 w-5 text-red-600" aria-hidden="true" />}
          </div>
        </Select.Trigger>
        {panelOpen &&
          panelWrapper(
            <Select.ResponsivePanel className="md:w-fit md:max-h-fit z-modal!" static>
              <div className="flex flex-col" ref={responsivePanelRef}>
                <div className="flex justify-center">
                  <Calendar
                    disabled={disabledDates}
                    selected={selectedValue}
                    mode="single"
                    className="md:flex-1"
                    onSelect={onSelect}
                  />
                </div>
              </div>
            </Select.ResponsivePanel>,
            document.body
          )}
      </Select.Root>
    );
  }
);

DatePicker.displayName = 'DatePicker';
