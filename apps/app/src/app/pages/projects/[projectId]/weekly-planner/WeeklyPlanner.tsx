import React, { Fragment, useEffect, useMemo } from 'react';
import { useMessageGetter } from '@messageformat/react';
import Divider from '@shape-construction/arch-ui/src/Divider';
import Page from '@shape-construction/arch-ui/src/Page';
import Tabs from '@shape-construction/arch-ui/src/Tabs';
import { useCurrentProject } from 'app/contexts/currentProject';
import { LayoutConfigs } from 'app/contexts/layout/layoutConfigs';
import { useLayoutContext } from 'app/contexts/layout/layoutContext';
import { Link, useParams } from 'react-router';
import { AllPlans } from './components/PlanList/AllPlans';
import { ArchivedPlans } from './components/PlanList/ArchivedPlans';
import { MyPlans } from './components/PlanList/MyPlans';
import { WeeklyPlannerHeader } from './components/WeeklyPlannerHeader/WeeklyPlannerHeader';
import { type TabHref, type TabOption, tabHref, tabIds } from './constants';

type PlanListScreens = {
  id: TabOption;
  to: TabHref;
  name: string;
  content: React.ReactElement;
  withLeftDivider?: boolean;
}[];

type Params = {
  tabId: TabOption;
};
const useWeeklyPlannerTabs = () => {
  const { tabId: selectedTab } = useParams() as Params;
  const project = useCurrentProject();

  const messages = useMessageGetter('weeklyPlanner.workPlans.tabs');

  const { setLayoutConfig } = useLayoutContext();
  useEffect(() => {
    setLayoutConfig({ ...LayoutConfigs.initialVariant });
  }, [setLayoutConfig]);

  const screens: PlanListScreens = useMemo(() => {
    if (!project) return [];

    return [
      {
        id: tabIds.myPlans,
        to: tabHref.myPlans,
        name: messages('myPlans'),
        content: <MyPlans />,
      },
      {
        id: tabIds.all,
        to: tabHref.all,
        name: messages('all'),
        content: <AllPlans />,
      },
      {
        id: tabIds.archived,
        to: tabHref.archived,
        name: messages('archived'),
        content: <ArchivedPlans />,
        withLeftDivider: true,
      },
    ];
  }, [project, messages]);

  const currentScreen = screens.find(({ to }) => selectedTab === to);

  return { screens, selectedTab, currentScreen };
};

export const WeeklyPlanner = () => {
  const { screens, currentScreen, selectedTab } = useWeeklyPlannerTabs();

  return (
    <Page>
      <WeeklyPlannerHeader />

      <div data-testid="res-main-box" className="bg-white relative z-0">
        <Tabs selectedValue={selectedTab}>
          {screens.map(({ id, name, to, withLeftDivider }) => {
            const isSelected = id === currentScreen?.id;

            return (
              <Fragment key={name}>
                {withLeftDivider && <Divider orientation="vertical" />}
                <Tabs.Tab value={to} selected={isSelected}>
                  <Link to={`../${to}`}>{name}</Link>
                </Tabs.Tab>
              </Fragment>
            );
          })}
        </Tabs>
      </div>

      <Page.Body>{currentScreen?.content}</Page.Body>
    </Page>
  );
};

export { WeeklyPlanner as Component };
