import type { LocationSchema } from '@shape-construction/api/src/types';
import type { NodeModel, TreeMethods } from '@shape-construction/arch-ui/src/Tree/Tree';
import { atom, useAtom } from 'jotai';

const checkedLocationsAtom = atom<NodeModel<LocationSchema>['id'][]>([]);

export const useProjectLocationsTreeState = (treeRef: React.RefObject<TreeMethods | null>) => {
  const [checkedLocations, setCheckedLocations] = useAtom(checkedLocationsAtom);

  return {
    checkedLocations,
    setCheckedLocations,

    checkAllLocations: () => {
      treeRef.current?.checkAll();
    },
    uncheckAllLocations: () => {
      treeRef.current?.uncheckAll();
    },

    openLocation: (targetIds: NodeModel<LocationSchema>['id'][]) => {
      treeRef.current?.open(targetIds);
    },
    closeLocation: (targetIds: NodeModel<LocationSchema>['id'][]) => {
      treeRef.current?.close(targetIds);
    },
    collapseAllLocations: () => {
      treeRef.current?.closeAll();
    },
    expandAllLocations: () => {
      treeRef.current?.openAll();
    },
  };
};
