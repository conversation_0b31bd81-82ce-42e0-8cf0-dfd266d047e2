import type { DisciplineSchema } from '@shape-construction/api/src/types';
import type { NodeModel, TreeMethods } from '@shape-construction/arch-ui/src/Tree/Tree';
import { atom, useAtom } from 'jotai';

const checkedDisciplinesAtom = atom<NodeModel<DisciplineSchema>['id'][]>([]);

export const useProjectDisciplinesTreeState = (treeRef: React.RefObject<TreeMethods | null>) => {
  const [checkedDisciplines, setCheckedDisciplines] = useAtom(checkedDisciplinesAtom);

  return {
    checkedDisciplines,
    setCheckedDisciplines,

    checkAllDisciplines: () => {
      treeRef.current?.checkAll();
    },
    uncheckAllDisciplines: () => {
      treeRef.current?.uncheckAll();
    },

    openDiscipline: (targetIds: NodeModel<DisciplineSchema>['id'][]) => {
      treeRef.current?.open(targetIds);
    },
    closeDiscipline: (targetIds: NodeModel<DisciplineSchema>['id'][]) => {
      treeRef.current?.close(targetIds);
    },
    collapseAllDisciplines: () => {
      treeRef.current?.closeAll();
    },
    expandAllDisciplines: () => {
      treeRef.current?.openAll();
    },
  };
};
