import React from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import CharacterCount from '@shape-construction/arch-ui/src/CharacterCount';
import InputText from '@shape-construction/arch-ui/src/InputText';
import { Field, type FieldProps } from 'formik';
import { LABEL_MAXIMUM_LENGTH } from '../utils/formUtils';

export const FormLabelField: React.FC<{ disabled?: boolean }> = ({ disabled = false }) => {
  const fieldTitleMessages = useMessageGetter('admin.customFields.form.fieldTitle');
  const charactersMessage = useMessage('admin.customFields.form.fieldTitle.characters');

  return (
    <Field name="label">
      {({ field, meta }: FieldProps) => (
        <InputText
          label={fieldTitleMessages('label')}
          cornerAdornment={
            <CharacterCount sentence={field.value} maximum={LABEL_MAXIMUM_LENGTH} label={charactersMessage} />
          }
          disabled={disabled}
          {...field}
          {...meta}
        />
      )}
    </Field>
  );
};
