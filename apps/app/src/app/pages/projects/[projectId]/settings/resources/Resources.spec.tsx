import React from 'react';
import { Resources } from 'app/pages/projects/[projectId]/settings/resources/Resources';
import { createMemoryHistory } from 'history';
import { render, screen, waitFor } from 'tests/test-utils';

describe('<Resources />', () => {
  it('renders all resource kinds as tabs', async () => {
    const route = { path: '/projects/:projectId/settings/resources/:resourceKind' };
    const history = createMemoryHistory({
      initialEntries: ['/projects/id/settings/resources/organisation'],
    });

    render(<Resources />, { route, history });

    expect(await screen.findByRole('tablist')).toBeInTheDocument();
    const tabs = screen.getAllByRole('tab').map((tab) => ({
      text: tab.textContent,
      selected: tab.getAttribute('aria-selected') === 'true',
    }));
    expect(tabs).toMatchObject([
      { text: 'admin.resources.person.title', selected: false },
      { text: 'admin.resources.organisation.title', selected: true },
      { text: 'admin.resources.role.title', selected: false },
      { text: 'admin.resources.equipment.title', selected: false },
      { text: 'admin.resources.material.title', selected: false },
    ]);
  });

  describe('when user clicks on another tab', () => {
    it('changes url to the new tab', async () => {
      const route = { path: '/projects/:projectId/settings/resources/:resourceKind' };
      const history = createMemoryHistory({
        initialEntries: ['/projects/id/settings/resources/organisation'],
      });
      const { user } = render(<Resources />, { route, history });

      await user.click(await screen.findByRole('link', { name: 'admin.resources.person.title' }));

      await waitFor(() => expect(history.location.pathname).toBe('/projects/id/settings/resources/person'));
    });

    it('switches to the clicked tab', async () => {
      const route = { path: '/projects/:projectId/settings/resources/:resourceKind' };
      const history = createMemoryHistory({
        initialEntries: ['/projects/id/settings/resources/organisation'],
      });
      const { rerender, user } = render(<Resources />, { route, history, staticRouter: false });

      await user.click(await screen.findByRole('link', { name: /admin.resources.person.title/ }));

      rerender(<Resources />);
      expect(
        await screen.findByRole('tab', { name: 'admin.resources.person.title', selected: true })
      ).toBeInTheDocument();
    });
  });
});
