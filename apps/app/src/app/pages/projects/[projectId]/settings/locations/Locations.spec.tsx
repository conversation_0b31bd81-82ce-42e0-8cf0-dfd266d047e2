import React from 'react';
import { locationFactory } from '@shape-construction/api/factories/locations';
import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import {
  deleteApiProjectsProjectIdLocationsLocationIdMockHandler,
  getApiProjectsProjectIdLocationsMockHandler,
  patchApiProjectsProjectIdLocationsLocationIdMockHandler,
  postApiProjectsProjectIdLocationsLocationIdSortMockHandler,
  postApiProjectsProjectIdLocationsMockHandler,
} from '@shape-construction/api/handlers-factories/projects/locations';
import type { LocationSchema } from '@shape-construction/api/src/types';
import type { NodeModel } from '@shape-construction/arch-ui/src/Tree/Tree';
import { hasChildren } from '@shape-construction/arch-ui/src/Tree/tree-utils';
import UtilsArray from 'app/components/Utils/UtilsArray';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor, within } from 'tests/test-utils';
import { Locations } from './Locations';

describe('Locations', () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  describe('Locations page header', () => {
    it('renders the title', async () => {
      const locationExample = locationFactory({
        projectId: 'project-0',
        name: 'location-example-01',
      });
      server.use(getApiProjectsProjectIdLocationsMockHandler(() => [locationExample]));
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };

      render(<Locations />, { history, route });

      expect(await screen.findByRole('heading', { name: 'admin.locations.title' })).toBeInTheDocument();
    });
  });

  describe('when data is fetched', () => {
    it('renders locations as a tree', async () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };
      const locationExample = locationFactory({ name: 'location-example-01' });
      server.use(getApiProjectsProjectIdLocationsMockHandler(() => [locationExample]));

      render(<Locations />, { history, route });

      expect(await screen.findByRole('treeitem', { name: 'location-example-01' })).toBeInTheDocument();
    });
  });

  describe('when a user wants to add a new location', () => {
    it('adds a new location and displays it on the tree list', async () => {
      const locationExample = locationFactory({ name: 'location-example-01' });
      const locations = [locationExample];
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };
      server.use(
        getApiProjectsProjectIdLocationsMockHandler(() => locations),
        postApiProjectsProjectIdLocationsMockHandler(async ({ request }) => {
          const { name, short_code: shortCode, parent_location_id: parentLocationId } = await request.json();
          const newLocation = locationFactory({ name, shortCode, parentLocationId });
          locations.push(newLocation);
          return newLocation;
        })
      );
      render(<Locations />, { history, route });

      const location = await screen.findByRole('treeitem', { name: 'location-example-01' });
      await userEvent.click(within(location).getByRole('button', { name: 'add children' }));

      const input = await screen.findByRole('textbox', { name: 'admin.locations.form.location' });
      await userEvent.type(input, 'teste!');
      userEvent.click(await screen.findByRole('button', { name: 'admin.locations.form.addCTA' }));

      await waitFor(() => {
        expect(screen.getAllByRole('treeitem')).toHaveLength(2);
      });

      const lastLocation = screen.getByRole('treeitem', { name: 'teste!' });
      expect(lastLocation).toHaveAttribute('aria-level', '2');
      expect(within(lastLocation).getByRole('textbox', { name: 'location-name' })).toHaveTextContent('teste!');
      expect(within(lastLocation).getByRole('textbox', { name: 'location-shortCode' })).toHaveTextContent('TESTE');
    });
  });

  describe('when a user wants to remove a specific location', () => {
    it('removes the location from the tree list', async () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };
      let locations = [
        locationFactory({ id: 'location-1', name: 'Location 01' }),
        locationFactory({ id: 'location-2', name: 'Location 02', parentLocationId: 'location-1' }),
      ];
      server.use(
        getApiProjectsProjectIdLocationsMockHandler(() => locations),
        // this will handle the mutation and return the updated location list in the next request
        deleteApiProjectsProjectIdLocationsLocationIdMockHandler(async ({ params }) => {
          locations = locations.filter(({ id }) => id !== params.locationId);
        })
      );

      const { user } = render(<Locations />, { history, route });
      await waitFor(() => {
        expect(screen.queryAllByRole('treeitem')).toHaveLength(2);
      });

      const location = await screen.findByRole('treeitem', { name: 'Location 02' });
      await user.click(within(location).getByRole('button', { name: 'Location options' }));
      await user.click(await screen.findByRole('menuitem', { name: 'admin.locations.actions.delete' }));

      await user.click(
        await screen.findByRole('button', {
          name: 'admin.locations.delete.confirmDeleteLocationOverlay.deleteCTA',
        })
      );

      await waitFor(() => expect(screen.queryAllByRole('treeitem')).toHaveLength(1));
    });
  });

  describe('when a user wants to remove multiple locations', () => {
    it('correctly determines if a location has children', () => {
      const locations = [
        {
          text: 'Project wide',
          id: 'e9ba8f90-b958-418e-9540-2c51873e6060',
          parent: 'root',
        },
        {
          text: 'Northern Ticket Hall',
          id: '1deb2761-d91b-4a14-b702-adbc710d4b66',
          parent: 'e9ba8f90-b958-418e-9540-2c51873e6060',
        },
        {
          text: 'NTH Loading Bays',
          id: 'd43167cd-64a2-4149-9b64-db773a86d571',
          parent: '1deb2761-d91b-4a14-b702-adbc710d4b66',
        },
        {
          text: 'Southern Ticket Hall',
          id: 'bc38f892-7d3e-4a18-9727-0de4da91ac9b',
          parent: 'e9ba8f90-b958-418e-9540-2c51873e6060',
        },
      ] as NodeModel<LocationSchema>[];

      expect(hasChildren(locations, 'e9ba8f90-b958-418e-9540-2c51873e6060')).toBe(true);
      expect(hasChildren(locations, '1deb2761-d91b-4a14-b702-adbc710d4b66')).toBe(true);
      expect(hasChildren(locations, 'd43167cd-64a2-4149-9b64-db773a86d571')).toBe(false);
      expect(hasChildren(locations, 'bc38f892-7d3e-4a18-9727-0de4da91ac9b')).toBe(false);
    });

    it('removes the selected location and his descendents from the tree list', async () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };
      let locations = [
        locationFactory({ id: 'location-1', name: 'Location 1' }),
        locationFactory({
          id: 'location-2',
          parentLocationId: 'location-1',
          name: 'Location 2',
        }),
        locationFactory({
          id: 'location-3',
          parentLocationId: 'location-2',
          name: 'Location 3',
        }),
        locationFactory({
          id: 'location-4',
          parentLocationId: 'location-3',
          name: 'Location 4',
        }),
      ];
      server.use(
        getApiProjectsProjectIdLocationsMockHandler(() => locations),
        // this will handle the mutation and return the updated location list in the next request
        deleteApiProjectsProjectIdLocationsLocationIdMockHandler(async ({ params }) => {
          locations = locations.filter(({ id }) => id !== params.locationId);
        })
      );

      render(<Locations />, { history, route });

      const location = await screen.findByRole('treeitem', { name: 'Location 2' });
      userEvent.click(within(location).getByRole('checkbox'));
      userEvent.click(await screen.findByRole('button', { name: 'admin.locations.actions.deleteSelected' }));

      userEvent.click(
        await screen.findByRole('button', {
          name: 'admin.locations.delete.confirmDeleteLocationOverlay.deleteCTA',
        })
      );

      await waitFor(() => expect(screen.getAllByRole('treeitem')).toHaveLength(1));
      expect(screen.queryByRole('treeitem', { name: 'Location 2' })).not.toBeInTheDocument();
      expect(screen.queryByRole('treeitem', { name: 'Location 3' })).not.toBeInTheDocument();
      expect(screen.queryByRole('treeitem', { name: 'Location 4' })).not.toBeInTheDocument();
    });
  });

  describe('when a user wants to edit a location', () => {
    it('edits a location and displays the new data on the tree list', async () => {
      const locationExample = locationFactory({ name: 'location-example-01' });
      const locations = [locationExample];

      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };

      server.use(getApiProjectsProjectIdLocationsMockHandler(() => locations));

      server.use(
        patchApiProjectsProjectIdLocationsLocationIdMockHandler(async ({ request }) => {
          const { name, short_code: shortCode } = await request.json();
          locationExample.name = name!;
          locationExample.shortCode = shortCode!;
          return locationExample;
        })
      );

      render(<Locations />, { history, route });

      const location = await screen.findByRole('treeitem', { name: 'location-example-01' });

      await userEvent.click(within(location).getByRole('button', { name: 'Location options' }));
      await userEvent.click(screen.getByRole('menuitem', { name: 'admin.locations.actions.edit' }));

      const input = await screen.findByRole('textbox', { name: 'admin.locations.form.location' });
      await userEvent.type(input, 'teste!');
      await userEvent.click(screen.getByRole('button', { name: 'admin.locations.form.editCTA' }));

      const lastLocation = await screen.findByRole('treeitem', { name: 'location-example-01teste!' });
      expect(lastLocation).toHaveAttribute('aria-level', '1');
      expect(lastLocation).toHaveTextContent('location-example-01teste!');
    });

    it('edits the name by clicking on it', async () => {
      const locationExample = locationFactory({ name: 'location-example-01' });
      const locations = [locationExample];
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };
      server.use(getApiProjectsProjectIdLocationsMockHandler(() => locations));
      server.use(
        patchApiProjectsProjectIdLocationsLocationIdMockHandler(async ({ request }) => {
          const { name, short_code: shortCode } = await request.json();
          locationExample.name = name!;
          locationExample.shortCode = shortCode!;
          return locationExample;
        })
      );
      render(<Locations />, { history, route });

      const location = await screen.findByRole('treeitem', { name: 'location-example-01' });
      const locationNameField = within(location).getByRole('textbox', {
        name: 'location-name',
      });
      await userEvent.click(locationNameField);
      await userEvent.type(locationNameField, '-edited!');

      expect(await screen.findByRole('treeitem', { name: 'location-example-01' })).toHaveTextContent(
        'location-example-01-edited'
      );
    });

    it('edits the shortCode by clicking on it', async () => {
      const locationExample = locationFactory({ name: 'location-example-01', shortCode: 'TEST' });
      const locations = [locationExample];

      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };

      server.use(getApiProjectsProjectIdLocationsMockHandler(() => locations));

      server.use(
        patchApiProjectsProjectIdLocationsLocationIdMockHandler(async ({ request }) => {
          const { name, short_code: shortCode } = await request.json();
          locationExample.name = name!;
          locationExample.shortCode = shortCode!;
          return locationExample;
        })
      );

      render(<Locations />, { history, route });

      const location = await screen.findByRole('treeitem', { name: 'location-example-01' });
      const locationShortCodeField = within(location).getByRole('textbox', {
        name: 'location-shortCode',
      });
      await userEvent.click(locationShortCodeField);
      await userEvent.type(locationShortCodeField, '-edited!');

      expect(locationShortCodeField).toHaveTextContent('TEST-ed');
    });
  });

  it('expands and collapses all locations', async () => {
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/settings/locations'],
    });
    const route = { path: '/projects/:projectId/settings/locations' };
    const location1 = locationFactory({ id: 'location-1' });
    const location2 = locationFactory({ id: 'location-2', parentLocationId: 'location-1' });
    const locations = [location1, location2];
    server.use(getApiProjectsProjectIdLocationsMockHandler(() => locations));
    render(<Locations />, { history, route });

    userEvent.click(await screen.findByRole('button', { name: 'admin.locations.actions.collapseAll' }));
    expect(await screen.findAllByRole('treeitem', { expanded: false })).toHaveLength(1);

    userEvent.click(await screen.findByRole('button', { name: 'admin.locations.actions.expandAll' }));
    expect(await screen.findAllByRole('treeitem', { expanded: true })).toHaveLength(2);
  });

  describe('when the user wants to delete the main location', () => {
    it('shows the action disabled', async () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };
      const location1 = locationFactory({
        id: 'location-1',
        name: 'Location 1',
        parentLocationId: null,
      });
      const locations = [location1];
      server.use(getApiProjectsProjectIdLocationsMockHandler(() => locations));
      render(<Locations />, { history, route });

      const location = await screen.findByRole('treeitem', { name: 'Location 1' });
      userEvent.click(within(location).getByRole('button', { name: 'Location options' }));

      expect(await screen.findByRole('menuitem', { name: 'admin.locations.actions.delete' })).toHaveAttribute(
        'aria-disabled',
        'true'
      );
    });
  });

  describe('when a user wants to move a location', () => {
    it('moves up the selected location', async () => {
      const secondPosition = 1;
      const thirdPosition = 2;
      const locationParent = locationFactory({
        id: 'parent-location',
        name: 'parent-location',
        parentLocationId: null,
      });
      const location01 = locationFactory({
        id: 'location-example-01',
        name: 'location-example-01',
        parentLocationId: 'parent-location',
      });
      const location02 = locationFactory({
        id: 'location-example-02',
        name: 'location-example-02',
        shortCode: 'LOC02',
        parentLocationId: 'parent-location',
      });
      let locations = [locationParent, location01, location02];
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };

      server.use(
        getApiProjectsProjectIdLocationsMockHandler(() => locations),
        postApiProjectsProjectIdLocationsLocationIdSortMockHandler(() => {
          locations = UtilsArray.moveElement(locations, thirdPosition, secondPosition);
        })
      );

      render(<Locations />, { history, route });

      const thirdLocation = (await screen.findAllByRole('treeitem'))[thirdPosition];
      expect(thirdLocation.textContent).toBe('location-example-02LOC02');

      await userEvent.click(within(thirdLocation).getByRole('button', { name: 'Location options' }));
      await userEvent.click(screen.getByRole('menuitem', { name: /admin.locations.actions.moveUp/ }));

      await waitFor(() => {
        const secondLocation = screen.getAllByRole('treeitem')[secondPosition];
        expect(secondLocation.textContent).toBe('location-example-02LOC02');
      });
    });

    it('moves down the selected location', async () => {
      const secondPosition = 1;
      const thirdPosition = 2;
      const locationParent = locationFactory({
        id: 'parent-location',
        name: 'parent-location',
        parentLocationId: null,
      });
      const location01 = locationFactory({
        id: 'location-example-01',
        name: 'location-example-01',
        shortCode: 'LOC01',
        parentLocationId: 'parent-location',
      });
      const location02 = locationFactory({
        id: 'location-example-02',
        name: 'location-example-02',
        parentLocationId: 'parent-location',
      });
      let locations = [locationParent, location01, location02];
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };

      server.use(
        getApiProjectsProjectIdLocationsMockHandler(() => locations),
        postApiProjectsProjectIdLocationsLocationIdSortMockHandler(() => {
          locations = UtilsArray.moveElement(locations, secondPosition, thirdPosition);
        })
      );

      render(<Locations />, { history, route });

      const secondLocation = (await screen.findAllByRole('treeitem'))[secondPosition];
      expect(secondLocation.textContent).toBe('location-example-01LOC01');

      await userEvent.click(within(secondLocation).getByRole('button', { name: 'Location options' }));
      await userEvent.click(screen.getByRole('menuitem', { name: /admin.locations.actions.moveDown/ }));

      await waitFor(() => {
        const thirdLocation = screen.getAllByRole('treeitem')[thirdPosition];
        expect(thirdLocation.textContent).toBe('location-example-01LOC01');
      });
    });
  });

  describe('when a user does not have permission to access locations settings', () => {
    it('redirects to project page', async () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/locations'],
      });
      const route = { path: '/projects/:projectId/settings/locations' };
      const locationExample = locationFactory({
        projectId: 'project-0',
        name: 'location-example-01',
      });

      server.use(
        getApiProjectsProjectIdMockHandler(() =>
          projectFactory({
            availableActions: projectAvailableActions({ manageLocations: false }),
          })
        ),
        getApiProjectsProjectIdLocationsMockHandler(() => [locationExample])
      );

      render(<Locations />, { route, history });

      await waitFor(() => expect(history.location.pathname).toBe('/projects/project-0'));
    });
  });
});

describe('when a user clicks on the first child options', () => {
  it('disables the move up action', async () => {
    const firstChild = 1;
    const locationParent = locationFactory({
      id: 'parent-location',
      name: 'parent-location',
      shortCode: 'LOC0',
      parentLocationId: null,
    });
    const location01 = locationFactory({
      id: 'location-example-01',
      name: 'location-example-01',
      shortCode: 'LOC01',
      parentLocationId: 'parent-location',
    });
    const location02 = locationFactory({
      id: 'location-example-02',
      name: 'location-example-02',
      shortCode: 'LOC02',
      parentLocationId: 'parent-location',
    });
    const locations = [locationParent, location01, location02];
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/settings/locations'],
    });
    const route = { path: '/projects/:projectId/settings/locations' };
    server.use(getApiProjectsProjectIdLocationsMockHandler(() => locations));

    render(<Locations />, { history, route });

    const firstLocation = (await screen.findAllByRole('treeitem'))[firstChild];
    expect(firstLocation).toHaveTextContent('location-example-01LOC01');

    await userEvent.click(within(firstLocation).getByRole('button', { name: 'Location options' }));

    expect(screen.getByRole('menuitem', { name: 'admin.locations.actions.moveUp' })).toHaveAttribute(
      'aria-disabled',
      'true'
    );
  });
});

describe('when a user clicks on the last child options', () => {
  it('disables the move down action', async () => {
    const lastChild = 2;
    const locationParent = locationFactory({
      id: 'parent-location',
      name: 'parent-location',
      shortCode: 'LOC0',
      parentLocationId: null,
    });
    const location01 = locationFactory({
      id: 'location-example-01',
      name: 'location-example-01',
      shortCode: 'LOC01',
      parentLocationId: 'parent-location',
    });
    const location02 = locationFactory({
      id: 'location-example-02',
      name: 'location-example-02',
      shortCode: 'LOC02',
      parentLocationId: 'parent-location',
    });
    const locations = [locationParent, location01, location02];
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/settings/locations'],
    });
    const route = { path: '/projects/:projectId/settings/locations' };
    server.use(getApiProjectsProjectIdLocationsMockHandler(() => locations));

    render(<Locations />, { history, route });

    const firstLocation = (await screen.findAllByRole('treeitem'))[lastChild];
    expect(firstLocation).toHaveTextContent('location-example-02LOC02');

    await userEvent.click(within(firstLocation).getByRole('button', { name: 'Location options' }));

    expect(screen.getByRole('menuitem', { name: 'admin.locations.actions.moveDown' })).toHaveAttribute(
      'aria-disabled',
      'true'
    );
  });
});
