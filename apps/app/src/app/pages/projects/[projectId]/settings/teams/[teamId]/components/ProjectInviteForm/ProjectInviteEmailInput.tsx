import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import InputText from '@shape-construction/arch-ui/src/InputText';
import { Field, type FieldProps, Form, Formik, type FormikValues } from 'formik';
import * as Yup from 'yup';

const schema = Yup.object().shape({
  email: Yup.string().email('Must be a valid email address').required('Required field'),
});

interface ProjectInviteFormEmailInputProps {
  onSubmit: (email: string) => void;
  disabled?: boolean;
}

export const ProjectInviteFormEmailInput: React.FC<ProjectInviteFormEmailInputProps> = ({ onSubmit, disabled }) => {
  const messages = useMessageGetter('admin.project.invite.form');

  const handleSubmit = (values: FormikValues) => {
    onSubmit(values.email);
  };

  return (
    <div>
      <Formik initialValues={{ email: '' }} validationSchema={schema} onSubmit={handleSubmit}>
        {({ values, handleReset, isValid }) => (
          <Form
            className="mt-8 flex items-start justify-between space-x-4"
            placeholder=""
            onPointerEnterCapture={() => {}}
            onPointerLeaveCapture={() => {}}
          >
            <Field name="email">
              {({ field, meta }: FieldProps) => (
                <InputText
                  {...field}
                  {...meta}
                  fullWidth
                  // @ts-expect-error Type 'string | false | undefined' is not assignable to type 'string | undefined'.
                  error={field.value.length > 0 && meta.error}
                  placeholder={messages('input')}
                  disabled={disabled}
                />
              )}
            </Field>
            <Button
              color="primary"
              variant="contained"
              size="md"
              type="submit"
              disabled={disabled || values.email.length === 0 || !isValid}
              onClick={() => {
                handleSubmit(values);
                handleReset();
              }}
              leadingIcon={PlusIcon}
            >
              {messages('addEmail')}
            </Button>
          </Form>
        )}
      </Formik>
    </div>
  );
};
