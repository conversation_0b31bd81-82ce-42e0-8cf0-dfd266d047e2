import React from 'react';
import type { CustomFieldSchema } from '@shape-construction/api/src/types';
import Table from '@shape-construction/arch-ui/src/Table';
import { CustomFieldActions } from './CustomFieldActions';
import { VisibilityCell } from './VisibilityCell';

type CustomFieldsTableRowProps = {
  customField: CustomFieldSchema;
  onEdit: () => void;
  onDelete: () => void;
};

export const CustomFieldsTableRow: React.FC<CustomFieldsTableRowProps> = ({ customField, onEdit, onDelete }) => {
  const isActionable = customField.availableActions.delete || customField.availableActions.edit;

  return (
    <Table.Row striped key={customField.id}>
      <Table.Cell aria-label="custom field name" className="font-medium text-gray-900">
        {customField.label}
      </Table.Cell>
      <Table.Cell aria-label="custom field visibility">
        <VisibilityCell isProjectWide={customField.projectWide} />
      </Table.Cell>
      <Table.Cell data-cy="custom-field-actions">
        <CustomFieldActions disabled={!isActionable} onEdit={onEdit} onDelete={onDelete} />
      </Table.Cell>
    </Table.Row>
  );
};
