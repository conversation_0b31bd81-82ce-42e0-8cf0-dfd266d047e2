import React, { useState } from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { ArrowDownIcon, ArrowUpIcon, TrashIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import type { TreeMethods } from '@shape-construction/arch-ui/src/Tree/Tree';
import { getTreeItem } from '@shape-construction/arch-ui/src/Tree/tree-utils';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { isFirstLocationChild, isLastLocationChild } from 'app/components/Utils/locations';
import { useProjectLocationsTree } from '../hooks/useProjectLocationsTree';
import { useProjectLocationsTreeState } from '../hooks/useProjectLocationsTreeState';
import { ProjectLocationsDeleteConfirmationModal } from './ProjectLocationsDeleteConfirmationModal';

interface ProjectLocationToolbarProps {
  projectId: ProjectSchema['id'];
  treeRef: React.RefObject<TreeMethods | null>;
}

const DeleteButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  const deleteText = useMessage('admin.locations.actions.deleteSelected');
  const isMediumScreen = useMediaQuery(breakpoints.up('md'));

  if (isMediumScreen)
    return (
      <Button variant="outlined" color="danger" size="xs" onClick={onClick}>
        {deleteText}
      </Button>
    );

  return <IconButton color="danger" size="md" variant="text" icon={TrashIcon} onClick={onClick} />;
};

const ExpandCollapseButtons: React.FC<{
  treeRef: React.RefObject<TreeMethods | null>;
}> = ({ treeRef }) => {
  const { collapseAllLocations, expandAllLocations } = useProjectLocationsTreeState(treeRef);
  const actionsMessages = useMessageGetter('admin.locations.actions');
  const isMediumScreen = useMediaQuery(breakpoints.up('md'));

  if (!isMediumScreen) return null;

  return (
    <>
      <Button variant="outlined" color="secondary" size="xs" onClick={collapseAllLocations}>
        {actionsMessages('collapseAll')}
      </Button>
      <Button variant="outlined" color="secondary" size="xs" onClick={expandAllLocations}>
        {actionsMessages('expandAll')}
      </Button>
    </>
  );
};

export const ProjectLocationToolbar: React.FC<ProjectLocationToolbarProps> = ({ projectId, treeRef }) => {
  const locationsMessages = useMessageGetter('admin.locations');
  const [showDeleteConfirmation, triggerDeleteConfirmation] = useState(false);
  const { locationsTree, deleteProjectLocationsInBatch, moveUpLocation, moveDownLocation } =
    useProjectLocationsTree(projectId);
  const { checkedLocations, checkAllLocations, uncheckAllLocations } = useProjectLocationsTreeState(treeRef);
  const isLargeScreen = useMediaQuery(breakpoints.up('lg'));

  const showSelectedLocationsActions = checkedLocations.length > 0;
  const showSortArrows = isLargeScreen && checkedLocations.length === 1;

  const openDeleteConfirmation = () => {
    triggerDeleteConfirmation(true);
  };

  const closeDeleteConfirmation = () => {
    triggerDeleteConfirmation(false);
  };

  const onDeleteLocations = () => {
    deleteProjectLocationsInBatch(checkedLocations as string[], { onSuccess: uncheckAllLocations });
    closeDeleteConfirmation();
  };

  let isFirstChild = false;
  let isLastChild = false;

  if (checkedLocations.length) {
    const node = getTreeItem(locationsTree, checkedLocations[0]);
    isFirstChild = !!node && isFirstLocationChild(node);
    isLastChild = !!node && isLastLocationChild(node);
  }

  return (
    <div
      data-cy="locations-toolbar"
      className="bg-gray-50 w-full px-2 md:px-8 py-2 h-12 flex items-center border-y shadow-green-800 gap-x-4 justify-between"
    >
      <div className="flex items-center gap-x-4">
        <div className="flex gap-x-2 items-center">
          <Button
            variant="text"
            size="xs"
            color="primary"
            onClick={uncheckAllLocations}
            disabled={!showSelectedLocationsActions}
          >
            {locationsMessages('actions.unselectCTA')}
          </Button>
          <Button variant="text" size="xs" color="primary" onClick={checkAllLocations}>
            {locationsMessages('actions.selectAllCTA')}
          </Button>
        </div>
        {showSelectedLocationsActions && (
          <span className="hidden sm:block text-sm leading-5 font-normal text-gray-900">
            {locationsMessages('selected', { selectedNum: checkedLocations.length })}
          </span>
        )}
      </div>

      <div>
        <div className="flex items-center gap-2">
          {showSortArrows && (
            <>
              <IconButton
                color="secondary"
                size="xs"
                variant="text"
                icon={ArrowUpIcon}
                disabled={isFirstChild}
                onClick={() => moveUpLocation(checkedLocations[0] as string)}
                title={locationsMessages('actions.moveUp')}
              />
              <IconButton
                color="secondary"
                size="xs"
                variant="text"
                icon={ArrowDownIcon}
                disabled={isLastChild}
                onClick={() => moveDownLocation(checkedLocations[0] as string)}
                title={locationsMessages('actions.moveDown')}
              />
            </>
          )}

          <ExpandCollapseButtons treeRef={treeRef} />
          {showSelectedLocationsActions && <DeleteButton onClick={openDeleteConfirmation} />}
        </div>

        {showDeleteConfirmation && (
          <ProjectLocationsDeleteConfirmationModal
            isOpen
            onCancel={closeDeleteConfirmation}
            onConfirm={onDeleteLocations}
          />
        )}
      </div>
    </div>
  );
};
