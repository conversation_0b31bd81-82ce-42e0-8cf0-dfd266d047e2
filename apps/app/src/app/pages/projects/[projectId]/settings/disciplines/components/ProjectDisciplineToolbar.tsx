import React, { useState } from 'react';
import { useMessage, useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import { ArrowDownIcon, ArrowUpIcon, TrashIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import type { TreeMethods } from '@shape-construction/arch-ui/src/Tree/Tree';
import { getTreeItem } from '@shape-construction/arch-ui/src/Tree/tree-utils';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { isFirstDisciplineChild, isLastDisciplineChild } from 'app/components/Utils/disciplines';
import { useProjectDisciplinesTree } from '../hooks/useProjectDisciplinesTree';
import { useProjectDisciplinesTreeState } from '../hooks/useProjectDisciplinesTreeState';
import { ProjectDisciplinesDeleteConfirmationModal } from './ProjectDisciplinesDeleteConfirmationModal';

interface ProjectDisciplineToolbarProps {
  projectId: ProjectSchema['id'];
  treeRef: React.RefObject<TreeMethods | null>;
}

const DeleteButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  const deleteText = useMessage('admin.disciplines.actions.deleteSelected');
  const isMediumScreen = useMediaQuery(breakpoints.up('md'));

  if (isMediumScreen)
    return (
      <Button variant="outlined" color="danger" size="xs" onClick={onClick}>
        {deleteText}
      </Button>
    );

  return <IconButton color="danger" size="md" variant="text" icon={TrashIcon} onClick={onClick} />;
};

const ExpandCollapseButtons: React.FC<{
  treeRef: React.RefObject<TreeMethods | null>;
}> = ({ treeRef }) => {
  const { collapseAllDisciplines, expandAllDisciplines } = useProjectDisciplinesTreeState(treeRef);
  const actionsMessages = useMessageGetter('admin.disciplines.actions');
  const isMediumScreen = useMediaQuery(breakpoints.up('md'));

  if (!isMediumScreen) return null;

  return (
    <>
      <Button variant="outlined" color="secondary" size="xs" onClick={collapseAllDisciplines}>
        {actionsMessages('collapseAll')}
      </Button>
      <Button variant="outlined" color="secondary" size="xs" onClick={expandAllDisciplines}>
        {actionsMessages('expandAll')}
      </Button>
    </>
  );
};

export const ProjectDisciplineToolbar: React.FC<ProjectDisciplineToolbarProps> = ({ projectId, treeRef }) => {
  const disciplinesMessages = useMessageGetter('admin.disciplines');
  const [showDeleteConfirmation, triggerDeleteConfirmation] = useState(false);
  const { disciplinesTree, deleteProjectDisciplinesInBatch, moveUpDiscipline, moveDownDiscipline } =
    useProjectDisciplinesTree(projectId);
  const { checkedDisciplines, checkAllDisciplines, uncheckAllDisciplines } = useProjectDisciplinesTreeState(treeRef);
  const isLargeScreen = useMediaQuery(breakpoints.up('lg'));

  let isFirstChild = false;
  let isLastChild = false;

  if (checkedDisciplines.length) {
    const node = getTreeItem(disciplinesTree, checkedDisciplines[0]);
    isFirstChild = !!node && isFirstDisciplineChild(node);
    isLastChild = !!node && isLastDisciplineChild(node);
  }

  const showSelectedDisciplinesActions = checkedDisciplines.length > 0;
  const showSortArrows = isLargeScreen && checkedDisciplines.length === 1;

  const openDeleteConfirmation = () => {
    triggerDeleteConfirmation(true);
  };

  const closeDeleteConfirmation = () => {
    triggerDeleteConfirmation(false);
  };

  const onDeleteDisciplines = () => {
    deleteProjectDisciplinesInBatch(checkedDisciplines as string[], {
      onSuccess: uncheckAllDisciplines,
    });
    closeDeleteConfirmation();
  };

  return (
    <div
      data-cy="disciplines-toolbar"
      className="bg-gray-50 w-full px-2 md:px-8 py-2 h-12 flex items-center border-y shadow-green-800 gap-x-4 justify-between"
    >
      <div className="flex items-center gap-x-4">
        <div className="flex gap-x-2 items-center">
          <Button
            variant="text"
            size="xs"
            color="primary"
            onClick={uncheckAllDisciplines}
            disabled={!showSelectedDisciplinesActions}
          >
            {disciplinesMessages('actions.unselectCTA')}
          </Button>
          <Button variant="text" size="xs" color="primary" onClick={checkAllDisciplines}>
            {disciplinesMessages('actions.selectAllCTA')}
          </Button>
        </div>
        {showSelectedDisciplinesActions && (
          <span className="hidden sm:block text-sm leading-5 font-normal text-gray-900">
            {disciplinesMessages('selected', { selectedNum: checkedDisciplines.length })}
          </span>
        )}
      </div>

      <div>
        <div className="flex items-center gap-2">
          {showSortArrows && (
            <>
              <IconButton
                color="secondary"
                size="xs"
                variant="text"
                icon={ArrowUpIcon}
                disabled={isFirstChild}
                onClick={() => moveUpDiscipline(checkedDisciplines[0] as string)}
                title={disciplinesMessages('actions.moveUp')}
              />
              <IconButton
                color="secondary"
                size="xs"
                variant="text"
                icon={ArrowDownIcon}
                disabled={isLastChild}
                onClick={() => moveDownDiscipline(checkedDisciplines[0] as string)}
                title={disciplinesMessages('actions.moveDown')}
              />
            </>
          )}

          <ExpandCollapseButtons treeRef={treeRef} />
          {showSelectedDisciplinesActions && <DeleteButton onClick={openDeleteConfirmation} />}
        </div>

        {showDeleteConfirmation && (
          <ProjectDisciplinesDeleteConfirmationModal
            isOpen
            onCancel={closeDeleteConfirmation}
            onConfirm={onDeleteDisciplines}
          />
        )}
      </div>
    </div>
  );
};
