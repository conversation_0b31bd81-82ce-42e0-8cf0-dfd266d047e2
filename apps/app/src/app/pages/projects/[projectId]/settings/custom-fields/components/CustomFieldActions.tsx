import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import IconButton from '@shape-construction/arch-ui/src/Button/IconButton';
import Dropdown from '@shape-construction/arch-ui/src/Dropdown';
import { TrashIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { EllipsisVerticalIcon, PencilSquareIcon } from '@shape-construction/arch-ui/src/Icons/solid';

type CustomFieldActionsProps = {
  disabled: boolean;
  onEdit: () => void;
  onDelete: () => void;
};

export const CustomFieldActions: React.FC<CustomFieldActionsProps> = ({ disabled, onEdit, onDelete }) => {
  const messages = useMessageGetter('admin.customFields.table.actions');

  return (
    <Dropdown.Root>
      <Dropdown.Trigger asChild>
        <IconButton
          size="xs"
          color="secondary"
          variant="text"
          disabled={disabled}
          icon={EllipsisVerticalIcon}
          aria-label="custom-field-actions"
        />
      </Dropdown.Trigger>
      <Dropdown.Items>
        <Dropdown.Item onClick={onEdit} data-cy="edit-custom-field" icon={PencilSquareIcon}>
          {messages('edit')}
        </Dropdown.Item>
        <Dropdown.Item onClick={onDelete} data-cy="remove-custom-field" color="danger" icon={TrashIcon}>
          {messages('remove')}
        </Dropdown.Item>
      </Dropdown.Items>
    </Dropdown.Root>
  );
};
