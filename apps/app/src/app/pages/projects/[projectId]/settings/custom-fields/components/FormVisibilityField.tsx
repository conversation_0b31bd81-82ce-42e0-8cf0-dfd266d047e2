import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { CustomFieldListSchema } from '@shape-construction/api/src/types';
import RadioGroup from '@shape-construction/arch-ui/src/RadioGroup';
import { Field, type FieldProps } from 'formik';
import { customFieldsCountForVisibilityType } from '../utils/formUtils';

type FormVisibilityFieldProps = {
  customFieldsData: CustomFieldListSchema;
  projectWideDisabled: boolean;
  teamWideDisabled: boolean;
  disabled: boolean;
};
export const FormVisibilityField: React.FC<FormVisibilityFieldProps> = ({
  customFieldsData,
  projectWideDisabled,
  teamWideDisabled,
  disabled,
}) => {
  const visibilityMessages = useMessageGetter('admin.customFields.form.visibility');

  const visibilityOptions = [
    {
      label: visibilityMessages('options.projectWide.label', {
        actualNumber: customFieldsCountForVisibilityType(customFieldsData, 'projectWide'),
        maximumNumber: customFieldsData?.meta.maximumProjectWideCustomFields,
      }),
      value: 'true',
      description: visibilityMessages('options.projectWide.description'),
      disabled: projectWideDisabled,
    },
    {
      label: visibilityMessages('options.team.label', {
        actualNumber: customFieldsCountForVisibilityType(customFieldsData, 'team'),
        maximumNumber: customFieldsData?.meta.maximumTeamCustomFields,
      }),
      value: 'false',
      description: visibilityMessages('options.team.description'),
      disabled: teamWideDisabled,
    },
  ];

  return (
    <Field name="project_wide">
      {({ field, meta, form }: FieldProps) => (
        <RadioGroup.Root
          name={field.name}
          value={field.value?.toString()}
          touched={meta.touched}
          error={meta.error}
          disabled={disabled}
          onValueChange={(newValue) => form.setFieldValue(field.name, newValue === 'true')}
        >
          <RadioGroup.Label description={visibilityMessages('description')} className="text-base">
            {visibilityMessages('label')}
          </RadioGroup.Label>
          <RadioGroup.Items>
            {visibilityOptions.map((item) => (
              <RadioGroup.Item key={item.value} value={item.value} disabled={item.disabled}>
                <RadioGroup.Label description={item.description}>{item.label}</RadioGroup.Label>
              </RadioGroup.Item>
            ))}
          </RadioGroup.Items>
        </RadioGroup.Root>
      )}
    </Field>
  );
};
