import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Button from '@shape-construction/arch-ui/src/Button';
import ConfirmationModal from '@shape-construction/arch-ui/src/ConfirmationModal';
import IconBadge from '@shape-construction/arch-ui/src/IconBadge';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/outline';

type ProjectDisciplinesDeleteConfirmationModalProps = {
  isOpen: boolean;
  onCancel: () => void;
  onConfirm: () => void;
};
export const ProjectDisciplinesDeleteConfirmationModal: React.FC<ProjectDisciplinesDeleteConfirmationModalProps> = ({
  isOpen,
  onCancel,
  onConfirm,
}) => {
  const messages = useMessageGetter('admin.disciplines.delete.confirmDeleteDisciplineOverlay');

  return (
    <ConfirmationModal.Root open={isOpen} onClose={onCancel} data-cy="discipline-delete-confirmation">
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="danger">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>{messages('subTitle')}</ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={onCancel}>
          {messages('cancelCTA')}
        </Button>
        <Button color="danger" variant="contained" size="md" onClick={onConfirm} data-cy="confirm-custom-field-removal">
          {messages('deleteCTA')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
