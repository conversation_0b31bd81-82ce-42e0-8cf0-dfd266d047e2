import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import type { CustomFieldSchema } from '@shape-construction/api/src/types';
import Button from '@shape-construction/arch-ui/src/Button';
import ConfirmationModal from '@shape-construction/arch-ui/src/ConfirmationModal';
import IconBadge from '@shape-construction/arch-ui/src/IconBadge';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/outline';

type DeleteCustomFieldConfirmationModalProps = {
  open: boolean;
  customFieldLabel: CustomFieldSchema['label'];
  onCancel: () => void;
  onConfirm: () => void;
};
export const DeleteCustomFieldConfirmationModal: React.FC<DeleteCustomFieldConfirmationModalProps> = ({
  open,
  customFieldLabel,
  onCancel,
  onConfirm,
}) => {
  const messages = useMessageGetter('admin.customFields.delete');

  return (
    <ConfirmationModal.Root open={open} onClose={onCancel}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="danger">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>{messages('subTitle', { customFieldLabel })}</ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={onCancel}>
          {messages('cancel')}
        </Button>
        <Button
          color="danger"
          variant="contained"
          size="md"
          onClick={onConfirm}
          aria-label="confirm-custom-field-removal"
          data-cy="confirm-custom-field-removal"
        >
          {messages('remove')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
