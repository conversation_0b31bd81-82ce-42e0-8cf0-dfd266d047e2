import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Page from '@shape-construction/arch-ui/src/Page';
import Tabs from '@shape-construction/arch-ui/src/Tabs';
import { ResourcesTabContent } from 'app/pages/projects/[projectId]/settings/resources/components/ResourcesTabContent';
import {
  type ResourceTabId,
  resourceTabIds,
  resourceTabIdToKind,
} from 'app/pages/projects/[projectId]/settings/resources/utils/constants';
import { Link, Navigate, useParams } from 'react-router';

export const Resources = () => {
  const { projectId, resourceKind: currentTabId } = useParams() as {
    projectId: string;
    resourceKind: ResourceTabId;
  };
  const messages = useMessageGetter('admin.resources');

  if (!resourceTabIdToKind[currentTabId]) return <Navigate to="/" replace />;

  return (
    <Page>
      <Page.Header title={messages('title')} />
      <Page.Body className="h-full p-0 md:p-0">
        <div className="bg-white">
          <Tabs selectedValue={currentTabId}>
            {resourceTabIds.map((tabId) => {
              const path = `/projects/${projectId}/settings/resources/${tabId}`;
              return (
                <Tabs.Tab key={tabId} value={tabId}>
                  <Link to={path}>{messages(`${tabId}.title`)}</Link>
                </Tabs.Tab>
              );
            })}
          </Tabs>
        </div>

        <ResourcesTabContent tabId={currentTabId} />
      </Page.Body>
    </Page>
  );
};

export { Resources as Component };
