import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import { ProjectWideCustomFieldIcon } from '../../../../../../components/UI/Icons/ProjectWideCustomFieldIcon';
import { TeamCustomFieldIcon } from '../../../../../../components/UI/Icons/TeamCustomFieldIcon';

type VisibilityCellProps = {
  isProjectWide: boolean;
};
export const VisibilityCell: React.FC<VisibilityCellProps> = ({ isProjectWide }) => {
  const tableMessages = useMessageGetter('admin.customFields.table');

  return (
    <div className="flex items-center gap-x-2">
      {isProjectWide ? (
        <>
          <ProjectWideCustomFieldIcon className="h-4" />
          {tableMessages('projectWide')}
        </>
      ) : (
        <>
          <TeamCustomFieldIcon className="h-4" />
          {tableMessages('team')}
        </>
      )}
    </div>
  );
};
