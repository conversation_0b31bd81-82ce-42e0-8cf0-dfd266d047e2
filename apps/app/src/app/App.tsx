import React, { useEffect, useMemo } from 'react';
import { Msal<PERSON>rovider } from '@azure/msal-react';
import { useMessage } from '@messageformat/react';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { ErrorBoundary } from '@sentry/react';
import { getApiProjectsProjectIdQueryOptions } from '@shape-construction/api/src/hooks';
import type { UserSchema } from '@shape-construction/api/src/types';
import { Toaster } from '@shape-construction/arch-ui/src/Toast';
import { FeatureFlagEntityProvider } from '@shape-construction/feature-flags';
import { useTitle } from '@shape-construction/hooks';
import { onlineManager, useIsRestoring, useQuery } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import { ActionCableProvider } from 'action-cable/ActionCableContext';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { getNewMsalInstance } from 'app/msalAuthConfig';
import { endSession, hasAuthToken } from 'app/session';
import { Provider as JotaiProvider } from 'jotai';
import { LocaleProvider } from 'messages/LocaleProvider';
import { CookiesProvider } from 'react-cookie';
import { HelmetProvider } from 'react-helmet-async';
import { matchRoutes, useLocation, useNavigate } from 'react-router';
import { usePageViewAnalytics } from './analytics/hooks/usePageViewAnalytics';
import { useAnalytics } from './analytics/useAnalytics';
import { CrashPage } from './components/CrashPage/CrashPage';
import useSetupExports from './components/Exports/useSetupExports';
import { useMicrosoftLogin } from './components/LoginProviders/Microsoft/useMicrosoftLogin';
import { ErrorNotificationModal } from './components/Notifications/ErrorNotificationModal/ErrorNotificationModal';
import { ProductTour } from './components/ProductTour/ProductTour';
import { environment } from './config/environment';
import { LayoutProvider } from './contexts/layout/layoutContext';
import { Layout } from './hoc/Layout/Layout';
import { InstallAppProvider } from './hooks/useInstallApp';
import { Meta } from './Meta';
import { usePushNotifications } from './pages/notifications/hooks/usePushNotifications';
import { NotificationReaderObserver } from './pages/notifications/NotificationReaderObserver';
import PrivateRoutes from './pages/PrivateRoutes';
import PublicRoutes from './pages/PublicRoutes';
import { useSyncPausedMutations } from './queries/offline/useSyncPausedMutations';
import queryClient from './queries/query.client.builder';
import { createPersister } from './queries/query.client.persister';
import { useUsersMe } from './queries/users/users';

const persister = createPersister(environment.REACT_QUERY_PERSISTER_KEY);

const msalInstance = getNewMsalInstance();
msalInstance.initialize();

const Routes: React.FC<{ user: UserSchema | undefined }> = ({ user }) => {
  useSetupExports();

  usePageViewAnalytics();

  if (!user) return <PublicRoutes />;

  return (
    <div className="flex flex-1 flex-col md:overflow-hidden">
      <ProductTour />
      <PrivateRoutes key={user.id} />
    </div>
  );
};

const AppContent = ({ onDefaultsMutationSet }: { onDefaultsMutationSet: () => void }) => {
  const isRestoring = useIsRestoring();
  const { data: user, isLoading: isUserLoading } = useUsersMe();
  const { isMicrosoftLoginLoading, setIsMicrosoftLoginLoading, handleSuccess } = useMicrosoftLogin();
  const location = useLocation();
  const projectId = useMemo(
    () => matchRoutes([{ path: '/projects/:projectId/*' }], location)?.[0]?.params?.projectId,
    [location]
  );
  const { data: project } = useQuery(getApiProjectsProjectIdQueryOptions(projectId!));
  const { handleSubscription } = usePushNotifications();

  useTitle(useMessage('meta.title'));

  /**
   * this useEffect is used to handle the creation of the user session after the microsoft login
   * this is located here to avoid having the handleRedirectPromise in the useMicrosoftLogin hook,
   * since it's triggering some issues on the user logout
   */
  useEffect(() => {
    msalInstance.handleRedirectPromise().then((response) => {
      if (response?.account) {
        setIsMicrosoftLoginLoading(true);
        handleSuccess(response);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useSyncPausedMutations(queryClient, onDefaultsMutationSet);

  useAnalytics();

  /**
   * this will handle the refresh of the user subscription
   * to make sure that the user is subscribed to push notifications
   */
  useEffect(() => {
    if (user) handleSubscription();
  }, [user]);

  if (isUserLoading || isRestoring || isMicrosoftLoginLoading) return <LoadingSpinner variant="screen" />;

  if (user && !hasAuthToken()) {
    // Something is wrong!!!
    // The session token was removed but the user is still on cache. Force reset!
    endSession();
    return <LoadingSpinner variant="screen" />;
  }

  return (
    <JotaiProvider key={projectId}>
      <FeatureFlagEntityProvider userId={user?.id} teamId={project?.currentTeamId}>
        <LayoutProvider>
          <Layout>
            <React.Suspense fallback={<LoadingSpinner variant="screen" />}>
              <Routes user={user} />
            </React.Suspense>
          </Layout>
        </LayoutProvider>
      </FeatureFlagEntityProvider>
    </JotaiProvider>
  );
};

export const App = () => {
  const navigate = useNavigate();
  const [defaultsMutationSet, setDefaultsMutationSet] = React.useState(false);
  const [hydratedQueryClient, setHydratedQueryClient] = React.useState(false);

  // Initialize online manager state on app startup
  useEffect(() => {
    // Ensure online manager reflects the actual browser online state
    onlineManager.setOnline(navigator.onLine);
  }, []);

  useEffect(() => {
    if (defaultsMutationSet && hydratedQueryClient) {
      queryClient.resumePausedMutations();
    }
  }, [defaultsMutationSet, hydratedQueryClient]);

  return (
    <HelmetProvider>
      <CookiesProvider>
        <PersistQueryClientProvider
          client={queryClient}
          persistOptions={{ persister }}
          onSuccess={() => setHydratedQueryClient(true)}
        >
          <LocaleProvider>
            <Meta />
            <MsalProvider instance={msalInstance}>
              <GoogleOAuthProvider clientId={environment.GOOGLE_CLIENT_ID}>
                <main className="bg-white print:hidden">
                  <ErrorBoundary
                    fallback={({ resetError }) => <CrashPage resetError={resetError} />}
                    onReset={() => navigate('/')}
                  >
                    {!environment.DISABLE_REACT_QUERY_DEV_TOOLS && (
                      <ReactQueryDevtools initialIsOpen={false} buttonPosition="top-left" />
                    )}
                    <InstallAppProvider>
                      <Toaster />
                      <NotificationReaderObserver />
                      <ErrorNotificationModal />
                      <ActionCableProvider>
                        <AppContent onDefaultsMutationSet={() => setDefaultsMutationSet(true)} />
                      </ActionCableProvider>
                    </InstallAppProvider>
                  </ErrorBoundary>
                </main>
              </GoogleOAuthProvider>
            </MsalProvider>
          </LocaleProvider>
        </PersistQueryClientProvider>
      </CookiesProvider>
    </HelmetProvider>
  );
};
